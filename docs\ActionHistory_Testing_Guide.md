# ActionHistory Testing Guide

## Manual Testing Instructions

This guide provides step-by-step instructions for manually testing the ActionHistory system in the lumi-api project.

## Prerequisites

1. Ensure the migration has been run: `php artisan migrate`
2. Have a valid JWT token for authentication
3. Have test data (clinica, dentista, paciente) in the database

## Testing Scenarios

### 1. Test Patient Creation Logging

**Endpoint:** `POST /pacientes`

**Request Body:**
```json
{
    "nome": "Test Patient for ActionHistory",
    "dentista_id": 1,
    "observacoes": "Testing action history logging"
}
```

**Expected ActionHistory Entry:**
- `action_type`: "CREATE"
- `action_description`: "Created new patient: Test Patient for ActionHistory"
- `paciente_id`: [new patient ID]
- `dentista_id`: 1
- `clinica_id`: [clinic ID from dentist]
- `new_data`: Contains patient data
- `old_data`: null

### 2. Test Patient Update Logging

**Endpoint:** `PUT /pacientes/{id}`

**Request Body:**
```json
{
    "nome": "Updated Patient Name",
    "observacoes": "Updated observations for testing"
}
```

**Expected ActionHistory Entry:**
- `action_type`: "UPDATE"
- `action_description`: "Updated patient: Updated Patient Name"
- `old_data`: Contains original patient data
- `new_data`: Contains updated patient data

### 3. Test Patient Deletion Logging

**Endpoint:** `DELETE /pacientes/{id}`

**Expected ActionHistory Entry:**
- `action_type`: "DELETE"
- `action_description`: "Deleted patient: [patient name]"
- `old_data`: Contains deleted patient data
- `new_data`: null

### 4. Test Dentist Creation Logging

**Endpoint:** `POST /dentistas`

**Request Body:**
```json
{
    "nome": "Test Dentist",
    "username": "testdentist",
    "senha": "password123",
    "clinica_id": 1,
    "observacoes": "Test dentist for action history"
}
```

**Expected ActionHistory Entries:**
1. Dentist creation entry
2. User creation entry (for the dentist's user account)

### 5. Test Consultation Creation Logging

**Endpoint:** `POST /consultas`

**Request Body:**
```json
{
    "paciente_id": 1,
    "dentista_id": 1,
    "data": "2025-02-01",
    "horario": "2025-02-01 10:00:00",
    "observacoes": "Test consultation",
    "status": "agendada"
}
```

**Expected ActionHistory Entry:**
- `action_type`: "CREATE"
- `action_description`: "Created consultation for patient: [patient name]"
- `paciente_id`: 1
- `dentista_id`: 1

### 6. Test Clinica Creation Logging (Admin Only)

**Endpoint:** `POST /clinicas`

**Request Body:**
```json
{
    "nome": "Test Clinic for ActionHistory",
    "endereco": "123 Test Street",
    "imagem_url": null
}
```

**Expected ActionHistory Entry:**
- `action_type`: "CREATE"
- `action_description`: "Created new clinica: Test Clinic for ActionHistory"
- `clinica_id`: [new clinic ID]

## Verifying ActionHistory Entries

### 1. View All Action History

**Endpoint:** `GET /action-history`

**Query Parameters:**
- `per_page`: Number of entries per page (default: 50)
- `action_type`: Filter by CREATE, UPDATE, DELETE
- `user_id`: Filter by user
- `user_search`: Search by username (case-insensitive, with wildcards)
- `paciente_id`: Filter by patient
- `dentista_id`: Filter by dentist
- `entity_type`: Filter by model type
- `start_date` & `end_date`: Date range filter

**Example Requests:**
```
GET /action-history?action_type=CREATE&per_page=10
GET /action-history?user_search=daniel&action_type=UPDATE
GET /action-history?user_search=admin&start_date=2025-01-01&end_date=2025-01-31
```

### 2. View Action History for Specific Patient

**Endpoint:** `GET /action-history/patient/{paciente_id}`

### 3. View Action History Statistics

**Endpoint:** `GET /action-history/stats`

**Response includes:**
- Total actions count
- Actions by type breakdown
- Actions by user breakdown
- Recent actions list

### 4. Export Action History

**Endpoint:** `GET /action-history/export`

**Query Parameters:**
- `start_date`: Start date for export
- `end_date`: End date for export

**Returns:** CSV file download

## Database Verification

You can also verify the ActionHistory entries directly in the database:

```sql
-- View all action history entries
SELECT * FROM action_histories ORDER BY created_at DESC LIMIT 10;

-- View entries for a specific patient
SELECT * FROM action_histories WHERE paciente_id = 1;

-- View entries by action type
SELECT * FROM action_histories WHERE action_type = 'CREATE';

-- View entries with user information
SELECT ah.*, u.name as user_name
FROM action_histories ah
JOIN users u ON ah.user_id = u.id
ORDER BY ah.created_at DESC;
```

## Testing Data Sanitization

Create a patient with sensitive data to verify sanitization:

**Request Body:**
```json
{
    "nome": "Test Patient",
    "dentista_id": 1,
    "password": "should_be_removed",
    "api_token": "should_be_removed",
    "observacoes": "Normal field"
}
```

**Verification:**
Check that the `new_data` in ActionHistory does not contain `password` or `api_token` fields.

## Testing User Search Filter

Test the new `user_search` filter functionality:

### 1. Test Case-Insensitive Search

**Request:**
```
GET /action-history?user_search=DANIEL
```

**Expected Result:**
Should return actions performed by users with "daniel" in their username (case-insensitive).

### 2. Test Partial Match Search

**Request:**
```
GET /action-history?user_search=dan
```

**Expected Result:**
Should return actions performed by users with "dan" anywhere in their username (e.g., "daniel", "daniela", "jordan").

### 3. Test Combined Filters

**Request:**
```
GET /action-history?user_search=admin&action_type=DELETE&start_date=2025-01-01
```

**Expected Result:**
Should return DELETE actions performed by users with "admin" in their username since January 1st, 2025.

### 4. Test Empty Results

**Request:**
```
GET /action-history?user_search=nonexistentuser
```

**Expected Result:**
Should return empty results with proper pagination structure.

## Testing Error Handling

1. **Invalid User:** Try to create entries when not authenticated
2. **Permission Errors:** Non-admin users should only see their clinic's data
3. **Large Data:** Test with very large text fields to verify truncation

## Performance Testing

1. **Bulk Operations:** Create multiple entities quickly and verify logging doesn't slow down operations
2. **Large Datasets:** Test with large amounts of action history data
3. **Concurrent Users:** Test with multiple users performing actions simultaneously

## Troubleshooting

### Common Issues

1. **No ActionHistory entries created:**
   - Check if middleware is applied to routes
   - Verify user is authenticated
   - Check application logs for errors

2. **Missing relationship data:**
   - Verify entity relationships are properly set
   - Check if foreign keys are correctly populated

3. **Permission errors:**
   - Ensure user has proper clinic access
   - Verify system_admin flag for admin users

### Log Files

Check these log files for ActionHistory-related errors:
- `storage/logs/laravel.log`
- Look for entries containing "ActionHistory" or "Failed to log"

## API Response Examples

### Successful ActionHistory Query Response

```json
{
    "data": [
        {
            "id": 1,
            "user_id": 1,
            "action_type": "CREATE",
            "action_description": "Created new patient: Test Patient",
            "http_method": "POST",
            "endpoint": "pacientes",
            "paciente_id": 5,
            "dentista_id": 1,
            "clinica_id": 1,
            "entity_type": "App\\Models\\Paciente",
            "entity_id": 5,
            "old_data": null,
            "new_data": {
                "nome": "Test Patient",
                "dentista_id": 1,
                "clinica_id": 1,
                "observacoes": "Test patient"
            },
            "ip_address": "127.0.0.1",
            "user_agent": "PostmanRuntime/7.28.4",
            "created_at": "2025-01-27T10:30:00.000000Z",
            "updated_at": "2025-01-27T10:30:00.000000Z",
            "user": {
                "id": 1,
                "name": "Test User"
            },
            "patient": {
                "id": 5,
                "nome": "Test Patient"
            },
            "dentist": {
                "id": 1,
                "nome": "Test Dentist"
            },
            "clinica": {
                "id": 1,
                "nome": "Test Clinic"
            }
        }
    ],
    "links": {...},
    "meta": {...}
}
```

This comprehensive testing guide ensures that the ActionHistory system is working correctly across all implemented controllers and scenarios.
