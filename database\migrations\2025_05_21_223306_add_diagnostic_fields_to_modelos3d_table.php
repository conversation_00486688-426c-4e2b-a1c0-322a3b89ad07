<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('modelos3d', function (Blueprint $table) {
            $table->boolean('is_diagnostico')->default(false)->after('descricao');
            $table->string('tag_diagnostico')->nullable()->after('is_diagnostico');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('modelos3d', function (Blueprint $table) {
            $table->dropColumn(['is_diagnostico', 'tag_diagnostico']);
        });
    }
};
