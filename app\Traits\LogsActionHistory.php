<?php

namespace App\Traits;

use App\Services\ActionHistoryService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

trait LogsActionHistory
{
    /**
     * Get the ActionHistoryService instance.
     */
    protected function getActionHistoryService(): ActionHistoryService
    {
        return App::make(ActionHistoryService::class);
    }

    /**
     * Get the current user ID from JWT payload.
     */
    protected function getCurrentUserId(): ?int
    {
        try {
            $payload = auth()->payload();
            return $payload->get('sub'); // JWT subject is the user ID
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Log a create action.
     */
    protected function logCreateAction(
        Model $entity,
        ?array $newData = null,
        ?Request $request = null,
        ?string $description = null
    ): void {
        $userId = $this->getCurrentUserId();
        if (!$userId) {
            return;
        }

        $newData = $newData ?? $entity->toArray();
        $request = $request ?? request();

        $this->getActionHistoryService()->logCreate(
            $userId,
            $entity,
            $newData,
            $request,
            $description
        );
    }

    /**
     * Log an update action.
     */
    protected function logUpdateAction(
        Model $entity,
        array $oldData,
        ?array $newData = null,
        ?Request $request = null,
        ?string $description = null
    ): void {
        $userId = $this->getCurrentUserId();
        if (!$userId) {
            return;
        }

        $newData = $newData ?? $entity->toArray();
        $request = $request ?? request();

        $this->getActionHistoryService()->logUpdate(
            $userId,
            $entity,
            $oldData,
            $newData,
            $request,
            $description
        );
    }

    /**
     * Log a delete action.
     */
    protected function logDeleteAction(
        Model $entity,
        ?array $oldData = null,
        ?Request $request = null,
        ?string $description = null
    ): void {
        $userId = $this->getCurrentUserId();
        if (!$userId) {
            return;
        }

        $oldData = $oldData ?? $entity->toArray();
        $request = $request ?? request();

        $this->getActionHistoryService()->logDelete(
            $userId,
            $entity,
            $oldData,
            $request,
            $description
        );
    }

    /**
     * Capture the original data before an update operation.
     */
    protected function captureOriginalData(Model $entity): array
    {
        return $entity->getOriginal();
    }

    /**
     * Log a bulk operation.
     */
    protected function logBulkAction(
        string $actionType,
        string $entityType,
        array $entityIds,
        ?string $description = null,
        ?Request $request = null
    ): void {
        $userId = $this->getCurrentUserId();
        if (!$userId) {
            return;
        }

        $request = $request ?? request();
        $description = $description ?? "Bulk {$actionType} operation on {$entityType}";

        // For bulk operations, we'll create a single action history entry
        // with the entity IDs in the new_data field
        $actionHistoryService = $this->getActionHistoryService();
        
        // Create a dummy model instance for the service
        $dummyEntity = new class extends Model {
            protected $table = 'dummy';
            public $id = null;
        };

        $actionHistoryService->logCreate(
            $userId,
            $dummyEntity,
            [
                'bulk_operation' => true,
                'entity_type' => $entityType,
                'entity_ids' => $entityIds,
                'operation_type' => $actionType,
            ],
            $request,
            $description
        );
    }
}
