<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEmailAndObservacoesToDentistasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('dentistas', function (Blueprint $table) {
            $table->after('especialidade', function ($table) {
                $table->string('email')->nullable();
                $table->text('observacoes')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('dentistas', function (Blueprint $table) {
            $table->dropColumn('email');
            $table->dropColumn('observacoes');
        });
    }
}