<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MakeFieldsNullableOnQuestoesFormularioBoasVindasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('questoes_formulario_boas_vindas', function (Blueprint $table) {
            $table->text('alternativas')->nullable()->change();
            $table->text('resposta')->nullable()->change();
            $table->text('detalhes')->nullable()->change();
            $table->text('ponto_positivo')->nullable()->change();
            $table->text('ponto_neutro')->nullable()->change();
            $table->text('ponto_negativo')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('questoes_formulario_boas_vindas', function (Blueprint $table) {
            $table->string('questao')->nullable(false)->change();
            $table->text('alternativas')->nullable(false)->change();
            $table->text('resposta')->nullable(false)->change();
            $table->text('detalhes')->nullable(false)->change();
            $table->text('ponto_positivo')->nullable(false)->change();
            $table->text('ponto_neutro')->nullable(false)->change();
            $table->text('ponto_negativo')->nullable(false)->change();
        });
    }
}