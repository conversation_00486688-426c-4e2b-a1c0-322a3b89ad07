<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddDefaultProfilePictureUrlToPacientesTable extends Migration
{
    public function up()
    {
        Schema::table('pacientes', function (Blueprint $table) {
            $table->string('profile_picture_url')->nullable()->default('profile_pic%252Fdefault.png')->change();
        });
    }

    public function down()
    {
        Schema::table('pacientes', function (Blueprint $table) {
            $table->string('profile_picture_url')->default(null)->change();
        });
    }
}