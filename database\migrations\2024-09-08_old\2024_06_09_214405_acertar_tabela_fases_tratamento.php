<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fases_tratamento', function (Blueprint $table) {
            $table->dropColumn('concluida');
            $table->string('descricao');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fases_tratamento', function (Blueprint $table) {
            $table->text('concluida')->default(0);
            $table->dropColumn('descricao');
        });
    }
};
