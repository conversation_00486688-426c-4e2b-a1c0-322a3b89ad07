<?php

namespace App\Http\Controllers;

use App\Models\HistoricoPaciente;
use App\Models\Paciente;
use App\Models\Consulta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class HistoricoPacienteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = auth()->payload();
        $pacienteId = $request->query('paciente_id');
        $consultaId = $request->query('consulta_id');

        $query = HistoricoPaciente::with(['paciente', 'consulta']);

        // Filtrar por paciente_id se fornecido na requisição
        if ($pacienteId) {
            $query->where('paciente_id', $pacienteId);
        }

        // Filtrar por consulta_id se fornecido na requisição
        if ($consultaId) {
            $query->where('consulta_id', $consultaId);
        }

        // Se não for admin do sistema, filtra apenas os históricos dos pacientes da clínica do usuário
        if (!$user['system_admin']) {
            $query->whereHas('paciente', function ($q) use ($user) {
                $q->where('clinica_id', $user['clinica']['id']);
            });
        }

        $historicos = $query->orderBy('created_at', 'desc')->get();

        return response()->json($historicos);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $data = $request->all();

        // Obter o ID do usuário autenticado do token JWT
        $user = auth()->user();
        $data['user_id'] = $user->id;

        // Se não tiver data e horário, usar a data e hora atual
        if (!isset($data['data'])) {
            $data['data'] = now()->format('Y-m-d');
        }

        if (!isset($data['horario'])) {
            $data['horario'] = now()->format('H:i:s');
        }

        // Verificar se o histórico está relacionado a um tratamento
        if (isset($data['consulta_id']) && !empty($data['consulta_id'])) {
            $data['referente_tratamento'] = true;
        } else {
            $data['referente_tratamento'] = false;
        }

        $historico = HistoricoPaciente::create($data);

        return response(['status' => 'success', 'data' => $historico]);
    }

    /**
     * Display the specified resource.
     */
    public function show(HistoricoPaciente $historicoPaciente)
    {
        $historicoPaciente->load(['paciente', 'consulta']);

        return response()->json($historicoPaciente);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\HistoricoPaciente $historicoPaciente
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, HistoricoPaciente $historicoPaciente)
    {
        try {
            // Log para depuração
            Log::info('Dados recebidos na requisição PUT:', ['request' => $request->all()]);

            // Obter apenas os campos permitidos para atualização
            $data = $request->only([
                'paciente_id',
                'consulta_id',
                'data',
                'horario',
                'codigo_acao',
                'descricao',
                'modificacoes',
                'user_id'
            ]);

            // Log para depuração
            Log::info('Dados filtrados para atualização:', ['data' => $data]);

            // O campo modificacoes já é automaticamente convertido para JSON pelo Laravel
            // graças ao cast definido no modelo, então não precisamos fazer nada especial aqui

            // Atualizar o histórico com os dados filtrados
            $historicoPaciente->update($data);

            // Recarregar o modelo com os relacionamentos
            $historicoPaciente->load(['paciente', 'consulta']);

            return response(['status' => 'success', 'data' => $historicoPaciente]);
        } catch (\Exception $e) {
            // Log do erro
            Log::error('Erro ao atualizar histórico:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response(['status' => 'error', 'message' => 'Erro ao atualizar histórico: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HistoricoPaciente $historicoPaciente)
    {
        $historicoPaciente->delete();

        return response(['status' => 'success']);
    }

    /**
     * Get all history entries for a specific patient.
     *
     * @param int $paciente_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHistoricoByPaciente($paciente_id)
    {
        $user = auth()->payload();

        $query = HistoricoPaciente::with(['consulta'])
            ->where('paciente_id', $paciente_id);

        // Se não for admin do sistema, filtra apenas os históricos dos pacientes da clínica do usuário
        if (!$user['system_admin']) {
            $query->whereHas('paciente', function ($q) use ($user) {
                $q->where('clinica_id', $user['clinica']['id']);
            });
        }

        $historicos = $query->orderBy('created_at', 'desc')->get();

        return response()->json($historicos);
    }

    /**
     * Update consultation observations and create a history entry.
     *
     * @param Request $request
     * @param int $consulta_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateConsultaObservacoes(Request $request, $consulta_id)
    {
        $user = auth()->user();
        $data = $request->all();

        // Buscar a consulta
        $consulta = Consulta::with('paciente')->findOrFail($consulta_id);

        // Atualizar as observações da consulta
        $observacoesAntigas = $consulta->observacoes;
        $consulta->observacoes = $data['observacoes'];
        $consulta->save();

        return response(['status' => 'success', 'data' => $historico]);
    }
}
