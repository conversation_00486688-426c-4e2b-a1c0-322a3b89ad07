<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consultas', function (Blueprint $table) {
            // Drop the existing foreign key
            $table->dropForeign(['dentista_id']);

            // Make sure the column is nullable
            $table->unsignedBigInteger('dentista_id')->nullable()->change();

            // Add the foreign key with SET NULL on delete
            $table->foreign('dentista_id')
                  ->references('id')
                  ->on('dentistas')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consultas', function (Blueprint $table) {
            // Drop the foreign key with SET NULL
            $table->dropForeign(['dentista_id']);
            
            // Restore the original foreign key without SET NULL
            $table->foreign('dentista_id')
                  ->references('id')
                  ->on('dentistas');
        });
    }
};
