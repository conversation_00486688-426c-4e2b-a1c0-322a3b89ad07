<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pacientes', function (Blueprint $table) {
            $table->id();
            $table->string('id_convenio');
            $table->string('id_dentista');
            $table->string('nome');
            $table->date('data_nascimento');
            $table->string('cpf');
            $table->string('etnia');
            $table->string('indicado_por');
            $table->string('como_conheceu');
            $table->string('observacoes');
            $table->string('endereco_cep');
            $table->string('endereco_logradouro');
            $table->string('endereco_numero');
            $table->string('endereco_complemento');
            $table->string('endereco_cidade');
            $table->string('endereco_estado');
            $table->timestamps();
        });    
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dentistas');
    }
};
