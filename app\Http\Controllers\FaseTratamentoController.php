<?php

namespace App\Http\Controllers;

use App\Models\FaseTratamento;
use Illuminate\Http\Request;

class FaseTratamentoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(FaseTratamento $faseTratamento)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FaseTratamento $faseTratamento)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, FaseTratamento $faseTratamento)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FaseTratamento $faseTratamento)
    {
        //
    }
}
