<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFatoresClinicosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fatores_clinicos', function (Blueprint $table) {
            $table->id();
            $table->string('tag');
            $table->string('imagem_url');
            $table->string('fator_clinico');
            $table->string('observacao');
            $table->string('observacao_secundaria');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fatores_clinicos');
    }
}