<?php

namespace App\Http\Controllers;

use App\Models\MetaTerapeutica;
use App\Models\HistoricoPaciente;
use Illuminate\Http\Request;

class MetaTerapeuticaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(MetaTerapeutica $metaTerapeutica)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MetaTerapeutica $metaTerapeutica)
    {
        //
    }

    /**
     * Update the specified resource in storage.
    */
    public function update(Request $request, MetaTerapeutica $metaTerapeutica)
    {
        try {
            $validated = $request->validate([
                'descricao' => 'sometimes|string',
                'status' => 'sometimes|in:PENDENTE,CONCLUIDA,NAO_CONCLUIDA,OUTRA_AREA',
            ]);

            $statusAntigo = $metaTerapeutica->status;
            $metaTerapeutica->update($validated);

            return responseSuccess([
                'message' => 'Meta terapêutica atualizada com sucesso',
                'data' => $metaTerapeutica
            ]);
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MetaTerapeutica $metaTerapeutica)
    {
        //
    }
}



