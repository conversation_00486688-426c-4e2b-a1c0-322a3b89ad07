<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('formularios_boas_vindas', function (Blueprint $table) {
            $table->foreignId('clinica_id')->after('id')->constrained('clinicas');
        });
    }

    public function down()
    {
        Schema::table('formularios_boas_vindas', function (Blueprint $table) {
            $table->dropForeign(['clinica_id']);
            $table->dropColumn('clinica_id');
        });
    }
};