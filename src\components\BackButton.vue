<template>
  <div>
    <!-- <PERSON>ersão mobile (apenas ícone) - visível apenas em telas sm e menores -->
    <transition name="fade">
      <button
        v-if="isMobile && !hasEditModeClass"
        class="back-arrow d-md-none"
        @click="goBack"
        :title="title || 'Voltar'"
        aria-label="Voltar"
      >
        <font-awesome-icon :icon="['fas', 'arrow-left']" />
      </button>
    </transition>

    <!-- Versão desktop (apenas ícone) - visível apenas em telas md e maiores -->
    <button
      v-if="!isMobile"
      class="back-arrow d-none d-md-block"
      @click="goBack"
      :title="title || 'Voltar'"
      aria-label="Voltar"
    >
      <font-awesome-icon :icon="['fas', 'arrow-left']" />
    </button>
  </div>
</template>

<script>
export default {
  name: "BackButton",
  props: {
    title: {
      type: String,
      default: ""
    },
    // Permite forçar o modo mobile ou desktop
    forceMobile: {
      type: Boolean,
      default: false
    },
    forceDesktop: {
      type: Boolean,
      default: false
    },
    // Permite especificar uma rota específica para voltar
    route: {
      type: [String, Object],
      default: null
    },
    // Classe para verificar se o botão está em modo de edição
    class: {
      type: [String, Object, Array],
      default: ""
    }
  },
  data() {
    return {
      windowWidth: window.innerWidth
    };
  },
  computed: {
    isMobile() {
      if (this.forceMobile) return true;
      if (this.forceDesktop) return false;
      return this.windowWidth < 992;
    },
    hasEditModeClass() {
      // Verifica se a classe edit-mode está presente
      if (typeof this.class === 'string') {
        return this.class.includes('edit-mode');
      } else if (typeof this.class === 'object' && !Array.isArray(this.class)) {
        return this.class['edit-mode'] === true;
      } else if (Array.isArray(this.class)) {
        return this.class.includes('edit-mode');
      }
      return false;
    }
  },
  methods: {
    goBack() {
      if (this.route) {
        this.$router.push(this.route);
      } else if (window.history.length > 1) {
        this.$router.back();
      } else {
        // Fallback para a página de pacientes se não houver histórico
        this.$router.push('/pacientes');
      }
    },
    handleResize() {
      this.windowWidth = window.innerWidth;
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
  },
  updated() {
  }
};
</script>

<style scoped>
/* Estilos para o botão de voltar (comum para mobile e desktop) */
.back-arrow {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: #f8f9fa;
  color: #29618b;
  border: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  z-index: 100;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  box-shadow: none;
}

.back-arrow:hover {
  background-color: #f5f9fc;
  color: #1e4c6e;
  border-color: #29618b;
  transform: translateX(-2px);
  box-shadow: 0 2px 6px rgba(41, 97, 139, 0.15);
}

.back-arrow:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(41, 97, 139, 0.2);
  border-radius: 8px;
}

.back-arrow:active {
  transform: scale(0.95) translateX(-2px);
  background-color: #e9f0f7;
}

/* Versão mobile - ajustes específicos */
.back-arrow.d-md-none {
  position: relative;
  /* Não usamos position fixed para manter o botão no fluxo do layout */
}

/* Transições */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

.fade-enter-to, .fade-leave-from {
  opacity: 1;
  transform: translateX(0);
}


</style>
