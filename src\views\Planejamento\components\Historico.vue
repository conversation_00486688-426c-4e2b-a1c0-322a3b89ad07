<template>
  <div class="tratamento-content">
    <div class="row">
      <div class="col-md-12">
        <!-- Toggle Button para alternar entre histórico detalhado e resumido -->
        <div v-if="!isLoading && historicos.length > 0" class="toggle-container mb-3">
          <div class="toggle-wrapper">
            <label class="toggle-switch">
              <input type="checkbox" v-model="historicoDetalhado">
              <span class="toggle-slider"></span>
            </label>
            <span class="toggle-label">{{ historicoDetalhado ? 'Histórico detalhado' : 'Histórico resumido' }}</span>
          </div>
        </div>

        <div v-if="isLoading" class="w-100 text-center py-3">
          <div class="spinner-border text-primary" role="status"></div>
          <p class="mt-2">Carregando histórico do paciente...</p>
        </div>

        <div v-else-if="!historicos.length" class="empty-state">
          <div class="empty-state-message">
            <div class="icon-wrapper">
              <font-awesome-icon :icon="['fas', 'history']" class="empty-state-icon" />
            </div>
            <p>Não há registros de histórico para este paciente.</p>
          </div>
        </div>

        <div v-else-if="historicos.length > 0 && historicosExibidos.length === 0" class="empty-state">
          <div class="empty-state-message">
            <div class="icon-wrapper">
              <font-awesome-icon :icon="['fas', 'filter']" class="empty-state-icon" />
            </div>
            <p>Não há registros de tratamento para exibir no modo resumido.</p>
            <button class="btn btn-sm btn-outline-primary mt-2" @click="historicoDetalhado = true">
              Ver histórico detalhado
            </button>
          </div>
        </div>

        <div v-else class="historico-timeline">
          <div v-for="(historico, index) in historicosExibidos" :key="index" class="historico-item">
            <div class="historico-badge" :class="{ 'tratamento-badge': isTratamentoHistorico(historico) }">
              <font-awesome-icon :icon="['fas', 'circle']" />
            </div>
            <div class="historico-panel" :class="{ 'tratamento-panel': isTratamentoHistorico(historico) }">
              <div class="historico-heading">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="historico-title">
                    {{ getHistoricoTitle(historico) }}
                    <span v-if="historicoDetalhado && isTratamentoHistorico(historico)" class="tratamento-indicator">Tratamento</span>
                  </h6>
                </div>
                <p class="historico-date">
                  <small>{{ $filters.dateDmy(historico.data) }} às {{ formatTime(historico.horario) }}</small>
                </p>
              </div>
              <div class="historico-body">
                <p>{{ historico.descricao }}</p>

                <!-- Exibir modificações se existirem -->
                <div v-if="hasModificacoes(historico)" class="modificacoes-container">
                  <h6 class="modificacoes-title">Alterações realizadas:</h6>
                  <div class="modificacoes-timeline">
                    <div v-for="(mod, modIndex) in getModificacoes(historico)" :key="modIndex" class="modificacao-item">
                      <div class="modificacao-badge">
                        <font-awesome-icon :icon="['fas', 'circle']" />
                      </div>
                      <div class="modificacao-panel">
                        <div class="modificacao-heading">
                          <h6 class="modificacao-title">{{ mod.titulo }}</h6>
                        </div>
                        <div class="modificacao-body">
                          <p>{{ mod.descricao }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.tratamento-content {
  padding: 20px;
  padding-top: 5px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.empty-state-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  text-align: center;
  background-color: #f8fafc;
  border-radius: 8px;
  margin: 0.5rem 0;
  border: 1px dashed #d1dce8;
  width: 100%;
}

.empty-state-message .icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e6f2ff, #d1e6ff);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.empty-state-message .empty-state-icon {
  font-size: 1.5rem;
  color: #5a9bd5;
}

/* Estilos para o histórico principal */
.historico-timeline {
  position: relative;
  padding: 20px 0;
  max-width: 800px;
  margin: 0 auto;
}

.historico-timeline:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 18px;
  width: 2px;
  background: linear-gradient(to bottom, rgba(0, 123, 255, 0.2), rgba(0, 123, 255, 0.6));
  border-radius: 1px;
}

.historico-item {
  position: relative;
  margin-bottom: 30px;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.historico-badge {
  position: absolute;
  top: 0;
  left: 0;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  text-align: center;
  font-size: 0.9em;
  line-height: 36px;
  background-color: white;
  border: 2px solid #007bff;
  color: #007bff;
  z-index: 100;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
}

.historico-panel {
  position: relative;
  margin-left: 60px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.historico-panel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #d1dce8;
}

.historico-panel:before {
  content: '';
  position: absolute;
  top: 10px;
  left: -10px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #e9ecef;
}

.historico-panel:after {
  content: '';
  position: absolute;
  top: 10px;
  left: -9px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #fff;
}

.historico-title {
  margin-top: 0;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
  font-size: 1rem;
}

.historico-date {
  color: #6c757d;
  margin-bottom: 8px;
  font-size: 0.85rem;
}

.historico-body > p {
  margin-bottom: 0;
  color: #495057;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Estilos para as modificações */
.modificacoes-container {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #e9ecef;
}

.modificacoes-title {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 12px;
  font-weight: 500;
}

.modificacoes-timeline {
  position: relative;
  padding-left: 15px;
}

.modificacoes-timeline:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 8px;
  width: 1px;
  background: linear-gradient(to bottom, rgba(92, 184, 92, 0.2), rgba(92, 184, 92, 0.6));
}

.modificacao-item {
  position: relative;
  margin-bottom: 15px;
  padding-left: 25px;
}

.modificacao-badge {
  position: absolute;
  top: 0;
  left: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  text-align: center;
  font-size: 0.5em;
  line-height: 16px;
  background-color: white;
  border: 1px solid #5cb85c;
  color: #5cb85c;
  z-index: 100;
}

.modificacao-panel {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 10px 12px;
  border: 1px solid #e9ecef;
}

.modificacao-title {
  margin: 0 0 5px 0;
  color: #333;
  font-weight: 500;
  font-size: 0.85rem;
}

.modificacao-body > p {
  margin-bottom: 0;
  color: #495057;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Estilos para o toggle button */
.toggle-container {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px;
}

.toggle-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 20px;
  padding: 5px 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.toggle-wrapper:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 22px;
  margin: 0 10px 0 0;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 22px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #007bff;
}

input:focus + .toggle-slider {
  box-shadow: 0 0 1px #007bff;
}

input:checked + .toggle-slider:before {
  transform: translateX(18px);
}

.toggle-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
  transition: color 0.2s ease;
}

/* Estilos para históricos de tratamento */
.tratamento-badge {
  border-color: #28a745;
  color: #28a745;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

.tratamento-panel {
  border-left: 3px solid #28a745;
}

.tratamento-panel:before {
  border-right-color: #28a745;
}

.tratamento-indicator {
  display: inline-block;
  font-size: 0.65rem;
  font-weight: 500;
  color: #fff;
  background-color: #28a745;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  text-transform: uppercase;
  vertical-align: middle;
  letter-spacing: 0.5px;
}
</style>

<script>
import moment from 'moment';
import { getHistoricosPaciente } from "@/services/historicoPacienteService";

export default {
  name: "Historico",
  props: {
    paciente: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isLoading: false,
      historicos: [],
      historicoDetalhado: localStorage.getItem('historicoDetalhado') === 'true' // Recupera a preferência do usuário
    }
  },
  computed: {
    historicosExibidos() {
      if (this.historicoDetalhado) {
        // Modo detalhado: exibe todos os históricos
        return this.historicos;
      } else {
        // Modo resumido: exibe apenas os históricos com referente_tratamento = true/1
        return this.historicos.filter(historico =>
          historico.referente_tratamento === true ||
          historico.referente_tratamento === 1 ||
          historico.referente_tratamento === '1'
        );
      }
    }
  },
  methods: {
    formatTime(dateTime) {
      if (!dateTime) return '-';

      try {
        // Se for uma string de data completa, extrair apenas a parte da hora
        if (typeof dateTime === 'string') {
          // Verificar se é uma data completa (YYYY-MM-DD HH:MM:SS)
          if (dateTime.includes('T') || dateTime.includes(' ')) {
            const date = new Date(dateTime);
            if (!isNaN(date.getTime())) {
              return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
            }
          }

          // Se for apenas um horário (HH:MM:SS)
          if (dateTime.includes(':')) {
            const timeParts = dateTime.split(':');
            if (timeParts.length >= 2) {
              return `${timeParts[0]}:${timeParts[1]}`;
            }
          }
        }

        // Se for um objeto Date
        if (dateTime instanceof Date) {
          return dateTime.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
        }

        // Tentar com moment como último recurso
        return moment(dateTime).format('HH:mm');
      } catch (error) {
        console.error('Erro ao formatar horário:', error);
        return '-';
      }
    },
    getHistoricoTitle(historico) {
      // Determinar o título com base no código de ação
      switch (historico.codigo_acao) {
        case 'alteracao_consulta':
          return 'Alterações na consulta';
        case 'inicio_tratamento':
          return 'Início do tratamento';
        case 'alteracao_aparelho':
          return 'Alteração de aparelho';
        case 'ajuste_aparelho':
          return 'Ajuste de aparelho';
        case 'remocao_aparelho':
          return 'Remoção de aparelho';
        default:
          return historico.codigo_acao ? historico.codigo_acao : 'Registro de histórico';
      }
    },
    hasModificacoes(historico) {
      if (!historico.modificacoes) return false;

      try {
        const mods = typeof historico.modificacoes === 'string'
          ? JSON.parse(historico.modificacoes)
          : historico.modificacoes;

        return Array.isArray(mods) ? mods.length > 0 : !!mods.titulo;
      } catch (e) {
        console.error('Erro ao processar modificações:', e);
        return false;
      }
    },
    getModificacoes(historico) {
      if (!historico.modificacoes) return [];

      try {
        let mods = typeof historico.modificacoes === 'string'
          ? JSON.parse(historico.modificacoes)
          : historico.modificacoes;

        // Se não for um array, mas tiver um título, converter para array
        if (!Array.isArray(mods) && mods.titulo) {
          mods = [mods];
        }

        return Array.isArray(mods) ? mods : [];
      } catch (e) {
        console.error('Erro ao processar modificações:', e);
        return [];
      }
    },
    isTratamentoHistorico(historico) {
      // Verifica se o histórico está relacionado ao tratamento
      return historico.referente_tratamento === true ||
             historico.referente_tratamento === 1 ||
             historico.referente_tratamento === '1';
    },
    async carregarHistoricos() {
      if (!this.paciente || !this.paciente.id) return;

      this.isLoading = true;
      try {
        const response = await getHistoricosPaciente(this.paciente.id);
        if (response && Array.isArray(response)) {
          this.historicos = response;
        } else {
          this.historicos = [];
        }
      } catch (error) {
        console.error('Erro ao buscar históricos do paciente:', error);
        this.historicos = [];
      } finally {
        this.isLoading = false;
      }
    }
  },
  mounted() {
    this.carregarHistoricos();
  },
  watch: {
    'paciente.id': {
      handler(newVal) {
        if (newVal) {
          this.carregarHistoricos();
        }
      },
      immediate: true
    },
    historicoDetalhado(newVal) {
      // Salva a preferência do usuário no localStorage
      localStorage.setItem('historicoDetalhado', newVal);
    }
  }
};
</script>