<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddTextOptionsColumnToQuestoesFormularioBoasVindasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('questoes_formulario_boas_vindas', function (Blueprint $table) {
            $table->text('text_options')->default('[]')->nullable()->after('tipo');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('questoes_formulario_boas_vindas', function (Blueprint $table) {
            $table->dropColumn('text_options');
        });
    }
}