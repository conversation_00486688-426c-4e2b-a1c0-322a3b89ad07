import axios from '@/services/axios'

// Funções para aparatologia
export async function salvarAparatologia(pacienteId, aparatologiaData) {
    try {
        const response = await axios.post('/aparatologia', {
            paciente_id: pacienteId,
            ...aparatologiaData
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data;

    } catch (error) {
        console.error('Erro ao salvar aparatologia:', error);
    }

    return false;
}

export async function atualizarAparatologia(id, aparatologiaData) {
    try {
        const response = await axios.put(`/aparatologia/${id}`, aparatologiaData);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data;

    } catch (error) {
        console.error('Erro ao atualizar aparatologia:', error);
    }

    return false;
}

export async function iniciarTratamento(id, dataPlanejamento, dataInicio, dataPrevista) {
    try {
        const response = await axios.post('/tratamentos/iniciar-tratamento/' + id, {
            dataPlanejamento,
            dataInicio,
            dataPrevista
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao iniciar o tratamento:', error);
    }

    return false;
}

export async function updateMetaStatus(id, status) {
    try {
        const response = await axios.patch('/metas-terapeuticas/' + id, {
            status
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao atualizar o status da meta terapêutica:', error);
    }

    return false;
}

export async function excluirMetaTerapeutica(id) {
    try {
        const response = await axios.delete('/tratamentos/meta-terapeutica/' + id);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao excluir meta terapêutica:', error);
    }

    return false;
}

export async function adicionarMetaTerapeutica(paciente_id, metaTerapeutica) {
    try {
        const response = await axios.post('/tratamentos/add-meta', {
            paciente_id,
            descricao: metaTerapeutica
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao adicionar meta terapêutica:', error);
    }

    return false;
}

// Funções para necessidades de encaminhamentos
export async function adicionarNecessidadeEncaminhamento(paciente_id, necessidade) {
    try {
        const response = await axios.post('/necessidades-encaminhamentos', {
            paciente_id,
            ...necessidade
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao adicionar necessidade de encaminhamento:', error);
    }

    return false;
}

export async function atualizarNecessidadeEncaminhamento(id, necessidade) {
    try {
        const response = await axios.put(`/necessidades-encaminhamentos/${id}`, necessidade);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao atualizar necessidade de encaminhamento:', error);
    }

    return false;
}

export async function excluirNecessidadeEncaminhamento(id) {
    try {
        const response = await axios.delete(`/necessidades-encaminhamentos/${id}`);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao excluir necessidade de encaminhamento:', error);
    }

    return false;
}

export async function getAnalises(pacienteId) {
    try {
        const response = await axios.get('/tratamentos/analises/' + pacienteId);

        if (!response || !response.data || !response.data.data || response.data.status !== 'success' || response.data.data.length == 0)
            return false

        return response.data.data

    } catch (error) {
        console.error('Erro ao consultar as análises:', error);
    }

    return false
}

export async function salvarAnalises(analises, pacienteId) {
    try {
        const response = await axios.post('/tratamentos/analises', {
            paciente_id: pacienteId,
            analises: JSON.stringify(analises),
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false

        return response.data

    } catch (error) {
        console.error('Erro ao salvar as análises:', error);
    }

    return false
}