<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('historicos_pacientes', function (Blueprint $table) {
            $table->boolean('referente_tratamento')->default(false)->after('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('historicos_pacientes', function (Blueprint $table) {
            $table->dropColumn('referente_tratamento');
        });
    }
};
