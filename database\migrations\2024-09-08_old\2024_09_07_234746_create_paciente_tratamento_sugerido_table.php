<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paciente_tratamento_sugerido', function (Blueprint $table) {
            $table->id();
            $table->unsignedBiginteger('paciente_id');
            $table->unsignedBiginteger('tratamento_sugerido_id');

            $table->foreign('paciente_id')->references('id')->on('pacientes')->onDelete('cascade');

            $table->foreign('tratamento_sugerido_id')->references('id')
                ->on('tratamentos_sugeridos')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paciente_tratamento_sugerido');
    }
};
