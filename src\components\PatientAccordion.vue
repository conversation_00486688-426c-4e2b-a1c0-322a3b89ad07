<template>
  <div class="accordion patient-accordion" id="patientInfoAccordion">
    <!-- Personal Information Accordion Item -->
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingPersonalInfo">
        <button
          class="accordion-button collapsed"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#collapsePersonalInfo"
          aria-expanded="false"
          aria-controls="collapsePersonalInfo"
          @click="handleAccordionClick('collapsePersonalInfo')"
        >
          <div class="accordion-title-container">
            <div class="accordion-icon-wrapper">
              <font-awesome-icon :icon="['fas', 'user']" />
            </div>
            <p class="text-uppercase mb-0 accordion-title">
              Informações pessoais
            </p>
          </div>
          <font-awesome-icon
            :icon="['fas', isAccordionOpen('collapsePersonalInfo') ? 'chevron-up' : 'chevron-down']"
            class="accordion-icon"
          />
        </button>
      </h2>
      <div
        id="collapsePersonalInfo"
        class="accordion-collapse collapse"
        aria-labelledby="headingPersonalInfo"
        data-bs-parent="#patientInfoAccordion"
      >
        <div class="accordion-body">
          <PatientPersonalInfo
            :paciente="paciente"
            :isEditing="isEditingPessoal"
            :isMobile="true"
            :dentistas="dentistas"
            :clinicas="clinicas"
            :isSystemAdmin="isSystemAdmin"
            :showResponsibleInfo="false"
            @update:field="handleUpdateField"
          />
        </div>
      </div>
    </div>

    <!-- Responsible Information Accordion Item -->
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingResponsibleInfo">
        <button
          class="accordion-button collapsed"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#collapseResponsibleInfo"
          aria-expanded="false"
          aria-controls="collapseResponsibleInfo"
          @click="handleAccordionClick('collapseResponsibleInfo')"
        >
          <div class="accordion-title-container">
            <div class="accordion-icon-wrapper">
              <font-awesome-icon :icon="['fas', 'users']" />
            </div>
            <p class="text-uppercase mb-0 accordion-title">
              Informações do responsável
            </p>
          </div>
          <font-awesome-icon
            :icon="['fas', isAccordionOpen('collapseResponsibleInfo') ? 'chevron-up' : 'chevron-down']"
            class="accordion-icon"
          />
        </button>
      </h2>
      <div
        id="collapseResponsibleInfo"
        class="accordion-collapse collapse"
        aria-labelledby="headingResponsibleInfo"
        data-bs-parent="#patientInfoAccordion"
      >
        <div class="accordion-body">
          <PatientResponsibleInfo
            :paciente="paciente"
            :isEditing="isEditingResponsavel"
            :isMobile="true"
            @update:field="handleUpdateField"
          />
        </div>
      </div>
    </div>

    <!-- Contact Information Accordion Item -->
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingContactInfo">
        <button
          class="accordion-button collapsed"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#collapseContactInfo"
          aria-expanded="false"
          aria-controls="collapseContactInfo"
          @click="handleAccordionClick('collapseContactInfo')"
        >
          <div class="accordion-title-container">
            <div class="accordion-icon-wrapper">
              <font-awesome-icon :icon="['fas', 'address-book']" />
            </div>
            <p class="text-uppercase mb-0 accordion-title">
              Meios de contato
            </p>
          </div>
          <font-awesome-icon
            :icon="['fas', isAccordionOpen('collapseContactInfo') ? 'chevron-up' : 'chevron-down']"
            class="accordion-icon"
          />
        </button>
      </h2>
      <div
        id="collapseContactInfo"
        class="accordion-collapse collapse"
        aria-labelledby="headingContactInfo"
        data-bs-parent="#patientInfoAccordion"
      >
        <div class="accordion-body">
          <PatientContactInfo
            :paciente="paciente"
            :isEditing="isEditingMeiosContatos"
            :isMobile="true"
            :novoContato="novoContato"
            :novoContatoMask="novoContatoMask"
            :getContatoPlaceholder="getContatoPlaceholder"
            @select-meio-contato="selectMeioContato"
            @contato-change="contatoChange"
            @adicionar-contato="adicionarContato"
            @excluir-contato="excluirContato"
            @update:field="handleUpdateField"
          />
        </div>
      </div>
    </div>

    <!-- Address Information Accordion Item -->
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingAddressInfo">
        <button
          class="accordion-button collapsed"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#collapseAddressInfo"
          aria-expanded="false"
          aria-controls="collapseAddressInfo"
          @click="handleAccordionClick('collapseAddressInfo')"
        >
          <div class="accordion-title-container">
            <div class="accordion-icon-wrapper">
              <font-awesome-icon :icon="['fas', 'map-marker-alt']" />
            </div>
            <p class="text-uppercase mb-0 accordion-title">
              Endereço
            </p>
          </div>
          <font-awesome-icon
            :icon="['fas', isAccordionOpen('collapseAddressInfo') ? 'chevron-up' : 'chevron-down']"
            class="accordion-icon"
          />
        </button>
      </h2>
      <div
        id="collapseAddressInfo"
        class="accordion-collapse collapse"
        aria-labelledby="headingAddressInfo"
        data-bs-parent="#patientInfoAccordion"
      >
        <div class="accordion-body">
          <PatientAddressInfo
            :paciente="paciente"
            :isEditing="isEditingEndereco"
            :isMobile="true"
            @get-endereco="getEndereco"
            @update:field="handleUpdateField"
          />
        </div>
      </div>
    </div>

    <!-- Ficha de avaliação inicial Accordion Item -->
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingFichaAvaliacao">
        <button
          class="accordion-button collapsed"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#collapseFichaAvaliacao"
          aria-expanded="false"
          aria-controls="collapseFichaAvaliacao"
          @click="handleAccordionClick('collapseFichaAvaliacao')"
        >
          <div class="accordion-title-container">
            <div class="accordion-icon-wrapper">
              <font-awesome-icon :icon="['fas', 'clipboard-check']" />
            </div>
            <p class="text-uppercase mb-0 accordion-title">
              Ficha de avaliação inicial
            </p>
            <span v-if="paciente.formulario_respondido" class="badge badge-sm bg-success ms-2">Respondida</span>
            <span v-else class="badge badge-sm bg-warning ms-2">Não respondida</span>
          </div>
          <font-awesome-icon
            :icon="['fas', isAccordionOpen('collapseFichaAvaliacao') ? 'chevron-up' : 'chevron-down']"
            class="accordion-icon"
          />
        </button>
      </h2>
      <div
        id="collapseFichaAvaliacao"
        class="accordion-collapse collapse"
        aria-labelledby="headingFichaAvaliacao"
        data-bs-parent="#patientInfoAccordion"
      >
        <div class="accordion-body text-center">
          <button
            v-if="!paciente.formulario_respondido"
            class="btn btn-sm btn-primary mb-0 action-button"
            @click="handleFormLinkBtn"
          >
            <font-awesome-icon
              :icon="possuiWhatsapp ? ['fab', 'whatsapp'] : ['fas', 'copy']"
              class="me-2"
            />
            <span>{{ possuiWhatsapp ? "ENVIAR LINK" : "COPIAR LINK" }}</span>
          </button>
          <button
            v-else
            class="btn btn-sm btn-primary mb-0 action-button"
            @click="toggleFormularioView"
            data-bs-toggle="modal"
            data-bs-target="#modalFormularioView"
          >
            <font-awesome-icon :icon="['fas', 'eye']" class="me-2" />
            <span>VISUALIZAR</span>
          </button>
        </div>
      </div>
    </div>


  </div>

  <!-- Botão de Iniciar Diagnóstico e Planejamento -->
  <div class="diagnostico-btn-container">
    <button
      class="btn btn-success diagnostico-btn mt-3 mb-2 w-100"
      @click="iniciarDiagnosticoPlanejamento"
    >
      <i class="me-2 fas fa-play"></i>
      <span class="uppercase">
        Iniciar diagnóstico e planejamento
      </span>
    </button>
  </div>

  <!-- Botão FAB de Editar -->
  <transition name="fade">
    <button
      v-if="isAnyAccordionOpen && !isAnyAccordionEditing"
      class="edit-fab"
      @click="editCurrentAccordion"
    >
      <font-awesome-icon :icon="['fas', 'edit']" class="me-1" />
      <span class="edit-fab-text">Editar</span>
    </button>
  </transition>

  <!-- Botões flutuantes para Cancelar e Salvar -->
  <transition name="fade">
    <div class="floating-buttons" v-if="isAnyAccordionEditing && isAnyAccordionOpen">
      <button
        class="cancel-fab me-2"
        @click="cancelEditing"
      >
        <font-awesome-icon :icon="['fas', 'times']" class="me-1" />
        <span>Cancelar</span>
      </button>
      <button
        class="save-fab"
        :class="{'save-fab-disabled': !hasPendingChanges}"
        @click="saveChanges"
        :disabled="!hasPendingChanges"
      >
        <font-awesome-icon :icon="['fas', 'save']" class="me-2" />
        <span>Salvar</span>
      </button>
    </div>
  </transition>
</template>

<script>
import PatientPersonalInfo from './PatientPersonalInfo.vue';
import PatientResponsibleInfo from './PatientResponsibleInfo.vue';
import PatientContactInfo from './PatientContactInfo.vue';
import PatientAddressInfo from './PatientAddressInfo.vue';

export default {
  name: "PatientAccordion",
  components: {
    PatientPersonalInfo,
    PatientResponsibleInfo,
    PatientContactInfo,
    PatientAddressInfo
  },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    isFilledPessoal: {
      type: Boolean,
      default: false
    },
    isEditingPessoal: {
      type: Boolean,
      default: false
    },
    isFilledResponsavel: {
      type: Boolean,
      default: false
    },
    isEditingResponsavel: {
      type: Boolean,
      default: false
    },
    isFilledEndereco: {
      type: Boolean,
      default: false
    },
    isEditingEndereco: {
      type: Boolean,
      default: false
    },
    isEditingMeiosContatos: {
      type: Boolean,
      default: false
    },
    dentistas: {
      type: Array,
      default: () => []
    },
    clinicas: {
      type: Array,
      default: () => []
    },
    isSystemAdmin: {
      type: Boolean,
      default: false
    },
    novoContato: {
      type: Object,
      required: true
    },
    novoContatoMask: {
      type: String,
      default: ""
    },
    getContatoPlaceholder: {
      type: String,
      default: ""
    },
    hasPendingChanges: {
      type: Boolean,
      default: false
    },
    editModeActive: {
      type: Boolean,
      default: false
    },
    possuiWhatsapp: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'toggle-edit-pessoal',
    'toggle-edit-responsavel',
    'toggle-edit-endereco',
    'toggle-edit-mode',
    'select-meio-contato',
    'contato-change',
    'adicionar-contato',
    'excluir-contato',
    'get-endereco',
    'update:field',
    'save-changes',
    'edit-mode-change',
    'update:editModeActive',
    'handle-form-link-btn',
    'toggle-formulario-view',
    'iniciar-diagnostico-planejamento'
  ],
  data() {
    return {
      currentOpenAccordion: null,
      openAccordions: {
        collapsePersonalInfo: false,
        collapseResponsibleInfo: false,
        collapseContactInfo: false,
        collapseAddressInfo: false,
        collapseFichaAvaliacao: false
      },
      checkInterval: null
    };
  },
  computed: {
    isAnyAccordionEditing() {
      return this.isEditingPessoal ||
            this.isEditingResponsavel ||
            this.isEditingMeiosContatos ||
            this.isEditingEndereco;
    },
    isAnyAccordionOpen() {
      return this.openAccordions.collapsePersonalInfo ||
            this.openAccordions.collapseResponsibleInfo ||
            this.openAccordions.collapseContactInfo ||
            this.openAccordions.collapseAddressInfo ||
            this.openAccordions.collapseFichaAvaliacao;
    }
  },
  methods: {
    toggleEditPessoal() {
      this.$emit('toggle-edit-pessoal');
      this.$emit('edit-mode-change', !this.isEditingPessoal);
    },
    toggleEditResponsavel() {
      this.$emit('toggle-edit-responsavel');
      this.$emit('edit-mode-change', !this.isEditingResponsavel);
    },
    toggleEditEndereco() {
      this.$emit('toggle-edit-endereco');
      this.$emit('edit-mode-change', !this.isEditingEndereco);
    },
    toggleEditMode(section) {
      this.$emit('toggle-edit-mode', section);
      this.$emit('edit-mode-change', !this.isEditing[section]);
    },
    isAccordionOpen(targetId) {
      const element = document.getElementById(targetId);
      return element && element.classList.contains('show');
    },
    handleEditClick(event, targetId, callback) {
      event.stopPropagation();

      // Check if the accordion is already open
      const element = document.getElementById(targetId);
      if (element && !element.classList.contains('show')) {
        // If accordion is closed, open it first
        const button = document.querySelector(`[data-bs-target="#${targetId}"]`);
        if (button) {
          button.click();
          // Wait for the accordion to open before executing the callback
          setTimeout(() => {
            callback();
          }, 350);
        }
      } else {
        // If accordion is already open, just execute the callback
        callback();
      }
    },
    handleAccordionClick(targetId) {
      // Atualizar imediatamente o estado do accordion
      this.$nextTick(() => {
        // Verificar se o accordion está aberto ou fechado
        const element = document.getElementById(targetId);
        const isOpen = element && element.classList.contains('show');

        // Atualizar o estado do accordion
        this.openAccordions[targetId] = isOpen;

        // Esperar a animação terminar para fazer o scroll
        setTimeout(() => {
          if (isOpen) {
            // Get the header element
            const headerElement = document.getElementById('heading' + targetId.replace('collapse', ''));
            if (headerElement) {
              // Scroll to the header element with offset to account for fixed headers
              const yOffset = -20;
              const y = headerElement.getBoundingClientRect().top + window.pageYOffset + yOffset;
              window.scrollTo({top: y, behavior: 'smooth'});
            }
            // Atualizar o accordion aberto atual
            this.currentOpenAccordion = targetId;
          } else {
            // Se o accordion foi fechado, limpar o accordion atual
            if (this.currentOpenAccordion === targetId) {
              this.currentOpenAccordion = null;
            }
          }
        }, 350); // Adjust timing based on the transition duration
      });
    },

    editCurrentAccordion() {
      // Ativar o modo de edição para esconder o botão Voltar
      this.$emit('update:editModeActive', true);

      // Verificar qual accordion está aberto e ativar o modo de edição correspondente
      if (this.openAccordions.collapsePersonalInfo && !this.isEditingPessoal) {
        this.toggleEditPessoal();
        this.$emit('edit-mode-change', true);
      } else if (this.openAccordions.collapseResponsibleInfo && !this.isEditingResponsavel) {
        this.toggleEditResponsavel();
        this.$emit('edit-mode-change', true);
      } else if (this.openAccordions.collapseContactInfo && !this.isEditingMeiosContatos) {
        this.toggleEditMode('meiosContatos');
        this.$emit('edit-mode-change', true);
      } else if (this.openAccordions.collapseAddressInfo && !this.isEditingEndereco) {
        this.toggleEditEndereco();
        this.$emit('edit-mode-change', true);
      }

      console.log('Modo de edição ativado, editModeActive =', true);
    },
    selectMeioContato(tipo) {
      this.$emit('select-meio-contato', tipo);
    },
    contatoChange(event) {
      this.$emit('contato-change', event);
    },
    adicionarContato() {
      this.$emit('adicionar-contato');
    },
    excluirContato(id, tipo) {
      this.$emit('excluir-contato', id, tipo);
    },
    getEndereco(event) {
      this.$emit('get-endereco', event);
    },
    handleUpdateField(data) {
      this.$emit('update:field', data);
    },
    cancelEditing() {
      // Desativar o modo de edição para mostrar o botão Voltar
      this.$emit('update:editModeActive', false);

      // Cancelar edição de todos os accordions
      if (this.isEditingPessoal) {
        this.toggleEditPessoal();
      }
      if (this.isEditingResponsavel) {
        this.toggleEditResponsavel();
      }
      if (this.isEditingMeiosContatos) {
        this.toggleEditMode('meiosContatos');
      }
      if (this.isEditingEndereco) {
        this.toggleEditEndereco();
      }
      // Emit that we're no longer in edit mode
      this.$emit('edit-mode-change', false);

      console.log('Modo de edição cancelado, editModeActive =', false);
    },
    saveChanges() {
      // Desativar o modo de edição para mostrar o botão Voltar
      this.$emit('update:editModeActive', false);

      // Emitir evento para salvar alterações
      this.$emit('save-changes');
      // Emit that we're no longer in edit mode
      this.$emit('edit-mode-change', false);

      console.log('Alterações salvas, editModeActive =', false);
    },

    handleFormLinkBtn() {
      this.$emit('handle-form-link-btn');
    },

    toggleFormularioView() {
      this.$emit('toggle-formulario-view');
    },

    iniciarDiagnosticoPlanejamento() {
      this.$emit('iniciar-diagnostico-planejamento');
    },

    setupAccordionListeners() {
      // Adicionar listeners para os eventos de accordion do Bootstrap
      const accordionIds = [
        'collapsePersonalInfo',
        'collapseResponsibleInfo',
        'collapseContactInfo',
        'collapseAddressInfo',
        'collapseFichaAvaliacao'
      ];

      accordionIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
          // Verificar o estado inicial
          this.openAccordions[id] = element.classList.contains('show');

          // Adicionar listeners para os eventos do Bootstrap
          element.addEventListener('shown.bs.collapse', () => {
            this.openAccordions[id] = true;
          });

          element.addEventListener('hidden.bs.collapse', () => {
            this.openAccordions[id] = false;
          });
        }
      });
    },

    checkAccordionsState() {
      // Verificar o estado atual dos accordions
      const accordionIds = [
        'collapsePersonalInfo',
        'collapseResponsibleInfo',
        'collapseContactInfo',
        'collapseAddressInfo',
        'collapseFichaAvaliacao'
      ];

      accordionIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
          const isOpen = element.classList.contains('show');
          if (this.openAccordions[id] !== isOpen) {
            this.openAccordions[id] = isOpen;
          }
        }
      });
    }
  },
  mounted() {
    // Monitorar os accordions quando o componente é montado
    this.setupAccordionListeners();

    // Verificar o estado inicial dos accordions
    this.checkAccordionsState();

    // Verificar o estado dos accordions periodicamente
    this.checkInterval = setInterval(() => {
      this.checkAccordionsState();
    }, 500);
  },
  updated() {
    // Verificar o estado dos accordions quando o componente é atualizado
    this.checkAccordionsState();
  },
  beforeUnmount() {
    // Limpar o intervalo quando o componente for desmontado
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }
  },
  watch: {
    isAnyAccordionEditing: {
      handler(newVal) {
        // Atualizar a propriedade editModeActive quando o estado de edição mudar
        this.$emit('update:editModeActive', newVal);
      },
      immediate: true
    }
  }
};
</script>

<style scoped>
.patient-accordion .accordion-button:not(.collapsed) {
  background: linear-gradient(to right, #f1f5fa, #f8f9fa);
  color: #344767;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);
  border-left: 4px solid #5a9bd5;
}

.patient-accordion .accordion-button:focus {
  box-shadow: none;
  border-color: rgba(0, 0, 0, 0.125);
}

.patient-accordion .accordion-item {
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.patient-accordion .accordion-body {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

/* Add transition for accordion collapse */
.accordion-collapse {
  transition: height 0.35s ease;
}

.cancel-edit-text {
  font-size: 9pt;
}

.pointer {
  cursor: pointer;
}

/* Accordion button styling */
.accordion-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.accordion-button p {
  flex: 1;
}

.accordion-title-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.accordion-icon-wrapper {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 8px;
  margin-right: 12px;
  background-color: rgba(90, 155, 213, 0.1);
  color: #5a9bd5;
  flex-shrink: 0;
}

.accordion-title {
  font-weight: 600;
  font-size: 0.85rem;
  color: #495057;
  letter-spacing: 0.5px;
  margin-left: 0.25rem;
}

.accordion-icon {
  margin-left: 0.5rem;
  transition: transform 0.2s ease;
  color: #5a9bd5;
}

.accordion-button .ms-2 {
  margin-left: 0.5rem !important;
}

.btn-light.border {
  border-color: #dee2e6 !important;
}

/* Estilos para o botão FAB de editar */
.edit-fab {
  position: fixed;
  bottom: 20px;
  right: 20px;
  min-width: 120px;
  height: 50px;
  border-radius: 25px;
  background-color: #29618b;
  color: white;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  z-index: 1000;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: pulse 1.5s infinite;
  padding: 0 20px;
}

.edit-fab-text {
  margin-left: 5px;
}

.edit-fab:hover {
  background-color: #1e4c6e;
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
  animation: none;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(41, 97, 139, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(41, 97, 139, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(41, 97, 139, 0);
  }
}

/* Estilos para os botões flutuantes */
.floating-buttons {
  position: fixed;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 1000;
  padding: 10px;
}

/* Estilo para o botão Cancelar */
.cancel-fab {
  min-width: 120px;
  height: 50px;
  border-radius: 25px;
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0 20px;
}

.cancel-fab:hover {
  background-color: #e9ecef;
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Estilo para o botão Salvar */
.save-fab {
  min-width: 120px;
  height: 50px;
  border-radius: 25px;
  background-color: #29618b;
  color: white;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0 20px;
}

.save-fab:hover:not(:disabled) {
  background-color: #1e4c6e;
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.save-fab:disabled, .save-fab-disabled {
  background-color: #8badc2 !important;
  opacity: 0.7 !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  pointer-events: none !important;
}

/* Estilos para os botões de ação nos accordions */
.accordion-body .action-button {
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  padding: 0.6em 1.4em;
  border-radius: 6px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  margin: 0.5rem 0;
}

.accordion-body .action-button svg {
  font-size: 1.5em;
}

.accordion-body .action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

/* Estilo para o botão de diagnóstico */
.diagnostico-btn-container {
  padding: 0 1rem;
  margin-top: 1.5rem;
}

.diagnostico-btn {
  background-color: #4CAF50;
  border-color: #43A047;
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.diagnostico-btn:hover {
  background-color: #43A047;
  border-color: #388E3C;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.diagnostico-btn i {
  font-size: 1rem;
}

/* Transições */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.fade-enter-to, .fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}
</style>
