<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Clinica extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'clinicas';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'imagem_url',
        'nome',
        'endereco',
        'slug',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    public function pacientes(): HasMany
    {
        return $this->hasMany(Paciente::class);
    }

    public function dentistas(): HasMany
    {
        return $this->hasMany(Dentista::class);
    }

    public function financeiro_pagar(): HasMany
    {
        return $this->hasMany(FinanceiroPagar::class);
    }

    public function financeiro_receber(): HasMany
    {
        return $this->hasMany(FinanceiroReceber::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }
}
