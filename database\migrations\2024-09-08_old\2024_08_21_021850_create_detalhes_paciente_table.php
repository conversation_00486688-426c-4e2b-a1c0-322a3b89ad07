<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateDetalhesPacienteTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('detalhes_paciente', function (Blueprint $table) {
            $table->id();
            $table->foreignId('paciente_id')->constrained();

            $table->unsignedBigInteger('id_questao');
            $table->foreign('id_questao')->references('id')->on
            ('questoes_formulario_boas_vindas');
            $table->string('tipo');
            $table->text('detalhe');
            $table->string('nivel'); // or enum('negativo', 'positivo', 'neutro', 'atencao')
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('detalhes_paciente');
    }
}