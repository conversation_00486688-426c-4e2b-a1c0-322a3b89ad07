<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    protected $table = 'pacientes'; // Substitua por 'nome_da_sua_tabela'

    /**
     * Nome da conexão de banco de dados a ser usada
     *
     * @var string
     */
    protected $connection = ''; // Defina se necessário (ex: 'mysql')

    /**
     * Execute as migrações
     *
     * @return void
     */
    public function up()
    {
        Schema::table($this->table, function (Blueprint $table) {
            $table->integer('id_convenio')->nullable()->change();
            $table->integer('id_dentista')->nullable()->change();
            $table->date('data_nascimento')->nullable()->change();
            $table->string('cpf')->nullable()->change();
            $table->string('etnia')->nullable()->change();
            $table->string('indicado_por')->nullable()->change();
            $table->string('como_conheceu')->nullable()->change();
            $table->string('observacoes')->nullable()->change();
            $table->string('endereco_cep')->nullable()->change();
            $table->string('endereco_logradouro')->nullable()->change();
            $table->string('endereco_numero')->nullable()->change();
            $table->string('endereco_complemento')->nullable()->change();
            $table->string('endereco_cidade')->nullable()->change();
            $table->string('endereco_estado')->nullable()->change();
        });
    }

    /**
     * Reverter as migrações
     *
     * @return void
     */
    public function down()
    {
    }
};
