<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\ActionHistoryService;
use Illuminate\Support\Facades\Log;

class ActionHistoryMiddleware
{
    protected ActionHistoryService $actionHistoryService;

    public function __construct(ActionHistoryService $actionHistoryService)
    {
        $this->actionHistoryService = $actionHistoryService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only track CUD operations (POST, PUT, PATCH, DELETE)
        if (!in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            return $next($request);
        }

        // Skip if user is not authenticated
        if (!auth()->check()) {
            return $next($request);
        }

        // Skip certain endpoints that shouldn't be tracked
        if ($this->shouldSkipTracking($request)) {
            return $next($request);
        }

        // Store the request data for potential logging
        $request->attributes->set('action_history_enabled', true);
        $request->attributes->set('action_history_service', $this->actionHistoryService);

        return $next($request);
    }

    /**
     * Determine if the request should be skipped from tracking.
     */
    private function shouldSkipTracking(Request $request): bool
    {
        $skipPatterns = [
            'auth/login',
            'auth/logout',
            'auth/refresh',
            'auth/me',
            'forgot-password',
            'reset-password',
            'email/verify',
            'img/*', // Image serving endpoints
            'modelo3d/*', // 3D model serving endpoints (GET requests)
        ];

        $path = $request->path();

        foreach ($skipPatterns as $pattern) {
            if (fnmatch($pattern, $path)) {
                return true;
            }
        }

        return false;
    }
}
