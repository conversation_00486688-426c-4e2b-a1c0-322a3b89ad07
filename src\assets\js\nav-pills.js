/* eslint-disable */

export default function setNavPills() {
  var total = document.querySelectorAll('.nav-pills');

  function initNavs() {
    total.forEach(function (item, i) {
      // Check if there's at least one li element with a nav-link
      var first_li = item.querySelector('li:first-child .nav-link');
      if (!first_li) return; // Skip this item if no nav-link found

      // Check if moving-tab already exists
      if (item.querySelector('.moving-tab')) return;

      var moving_div = document.createElement('div');
      var tab = first_li.cloneNode();
      tab.innerHTML = "-";

      moving_div.classList.add('moving-tab', 'position-absolute', 'nav-link');
      moving_div.appendChild(tab);
      item.appendChild(moving_div);

      var list_length = item.getElementsByTagName("li").length;
      if (list_length === 0) return; // Skip if no li elements

      moving_div.style.padding = '0px';

      const firstChild = item.querySelector('li:nth-child(1)');
      if (firstChild) {
        moving_div.style.width = firstChild.offsetWidth + 'px';
      }

      moving_div.style.transform = 'translate3d(0px, 0px, 0px)';
      moving_div.style.transition = '.5s ease';
      moving_div.style.opacity = '1'; // Ensure the moving-tab is visible
      moving_div.style.height = '100%'; // Set height to 100%

      item.onmouseover = function (event) {
        let target = getEventTarget(event);
        if (!target) return;

        let li = target.closest('li'); // get reference
        if (li) {
          let ul = li.closest('ul');
          if (!ul) return;

          let nodes = Array.from(ul.children); // get array
          let index = nodes.indexOf(li) + 1;

          const navLink = item.querySelector('li:nth-child(' + index + ') .nav-link');
          if (!navLink) return;

          navLink.onclick = function () {
            moving_div = item.querySelector('.moving-tab');
            if (!moving_div) return;

            let sum = 0;
            if (item.classList.contains('flex-column')) {
              for (var j = 1; j <= nodes.indexOf(li); j++) {
                const childElement = item.querySelector('li:nth-child(' + j + ')');
                if (childElement) {
                  sum += childElement.offsetHeight;
                }
              }
              moving_div.style.transform = 'translate3d(0px,' + sum + 'px, 0px)';

              const jElement = item.querySelector('li:nth-child(' + j + ')');
              if (jElement) {
                moving_div.style.height = jElement.offsetHeight;
              }
            } else {
              for (var j = 1; j <= nodes.indexOf(li); j++) {
                const childElement = item.querySelector('li:nth-child(' + j + ')');
                if (childElement) {
                  sum += childElement.offsetWidth;
                }
              }
              moving_div.style.transform = 'translate3d(' + sum + 'px, 0px, 0px)';

              const indexElement = item.querySelector('li:nth-child(' + index + ')');
              if (indexElement) {
                moving_div.style.width = indexElement.offsetWidth + 'px';
              }
            }
          }
        }
      }
    });
  }

  setTimeout(function () {
    initNavs();
  }, 100);

  // Tabs navigation resize

  window.addEventListener('resize', function (event) {
    total.forEach(function (item, i) {
      // Check if moving-tab exists before trying to remove it
      const existingTab = item.querySelector('.moving-tab');
      if (existingTab) {
        existingTab.remove();
      }

      // Check if there's an active nav-link before proceeding
      const activeLink = item.querySelector(".nav-link.active");
      if (!activeLink) return; // Skip this iteration if no active link

      var moving_div = document.createElement('div');
      var tab = activeLink.cloneNode();
      tab.innerHTML = "-";

      moving_div.classList.add('moving-tab', 'position-absolute', 'nav-link');
      moving_div.appendChild(tab);

      item.appendChild(moving_div);

      moving_div.style.padding = '0px';
      moving_div.style.transition = '.5s ease';
      moving_div.style.opacity = '1'; // Ensure the moving-tab is visible

      let li = item.querySelector(".nav-link.active").parentElement;

      if (li) {
        let nodes = Array.from(li.closest('ul').children); // get array
        let index = nodes.indexOf(li) + 1;

        let sum = 0;
        if (item.classList.contains('flex-column')) {
          for (var j = 1; j <= nodes.indexOf(li); j++) {
            sum += item.querySelector('li:nth-child(' + j + ')').offsetHeight;
          }
          moving_div.style.transform = 'translate3d(0px,' + sum + 'px, 0px)';
          moving_div.style.width = item.querySelector('li:nth-child(' + index + ')').offsetWidth + 'px';
          moving_div.style.height = item.querySelector('li:nth-child(' + j + ')').offsetHeight;
        } else {
          for (var j = 1; j <= nodes.indexOf(li); j++) {
            sum += item.querySelector('li:nth-child(' + j + ')').offsetWidth;
          }
          moving_div.style.transform = 'translate3d(' + sum + 'px, 0px, 0px)';
          moving_div.style.width = item.querySelector('li:nth-child(' + index + ')').offsetWidth + 'px';
          moving_div.style.height = '100%'; // Ensure the height is set to 100%
        }
      }
    });

    if (window.innerWidth < 991) {
      total.forEach(function (item, i) {
        if (!item.classList.contains('flex-column')) {
          item.classList.remove('flex-row');
          item.classList.add('flex-column', 'on-resize');

          // Check if there's an active nav-link
          const activeLink = item.querySelector(".nav-link.active");
          if (!activeLink) return; // Skip if no active link

          let li = activeLink.parentElement;
          if (!li) return; // Skip if no parent element

          let nodes = Array.from(li.closest('ul').children); // get array
          let index = nodes.indexOf(li) + 1;
          let sum = 0;
          for (var j = 1; j <= nodes.indexOf(li); j++) {
            const childElement = item.querySelector('li:nth-child(' + j + ')');
            if (childElement) {
              sum += childElement.offsetHeight;
            }
          }

          var moving_div = item.querySelector('.moving-tab');
          if (!moving_div) return; // Skip if no moving tab

          const firstChild = item.querySelector('li:nth-child(1)');
          if (firstChild) {
            moving_div.style.width = firstChild.offsetWidth + 'px';
          }

          moving_div.style.transform = 'translate3d(0px,' + sum + 'px, 0px)';
        }
      });
    } else {
      total.forEach(function (item, i) {
        if (item.classList.contains('on-resize')) {
          item.classList.remove('flex-column', 'on-resize');
          item.classList.add('flex-row');

          // Check if there's an active nav-link
          const activeLink = item.querySelector(".nav-link.active");
          if (!activeLink) return; // Skip if no active link

          let li = activeLink.parentElement;
          if (!li) return; // Skip if no parent element

          let nodes = Array.from(li.closest('ul').children); // get array
          let index = nodes.indexOf(li) + 1;
          let sum = 0;

          for (var j = 1; j <= nodes.indexOf(li); j++) {
            const childElement = item.querySelector('li:nth-child(' + j + ')');
            if (childElement) {
              sum += childElement.offsetWidth;
            }
          }

          var moving_div = item.querySelector('.moving-tab');
          if (!moving_div) return; // Skip if no moving tab

          moving_div.style.transform = 'translate3d(' + sum + 'px, 0px, 0px)';

          const indexElement = item.querySelector('li:nth-child(' + index + ')');
          if (indexElement) {
            moving_div.style.width = indexElement.offsetWidth + 'px';
          }
        }
      })
    }
  });

  // Function to remove flex row on mobile devices
  if (window.innerWidth < 991) {
    total.forEach(function (item, i) {
      if (item && item.classList && item.classList.contains('flex-row')) {
        item.classList.remove('flex-row');
        item.classList.add('flex-column', 'on-resize');
      }
    });
  }

  function getEventTarget(e) {
    e = e || window.event;
    return e.target || e.srcElement;
  }
}