<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPacienteIdToMentoriasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('mentorias', function (Blueprint $table) {
            $table->unsignedBigInteger('paciente_id')->after('mentor_id');
            $table->foreign('paciente_id')->references('id')->on('pacientes');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mentorias', function (Blueprint $table) {
            $table->dropForeign('mentorias_paciente_id_foreign');
            $table->dropColumn('paciente_id');
        });
    }
}