<?php

namespace App\Http\Controllers;

use App\Models\ContatoDentista;
use Illuminate\Http\Request;

class ContatoDentistaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $contatoDentista = new ContatoDentista();
        $contatoDentista->dentista_id = $request->input('dentista_id');
        $contatoDentista->tipo = $request->input('tipo');
        $contatoDentista->contato = $request->input('contato');
        $contatoDentista->descricao = $request->input('descricao');

        $contatoDentista->save();

        // Retorne uma resposta, por exemplo:
        return responseSuccess();
    }

    public function deleteContato(Request $request, $id)
    {
        try {
            // Recupera o contato paciente pelo ID
            $contatoDentista = ContatoDentista::find($id);

            // Verifica se o contato paciente existe
            if (!$contatoDentista)
                throw new \Exception('Contato não encontrado');

            // Remove o contato paciente do banco de dados
            $contatoDentista->delete();

            // Retorna uma resposta de sucesso
            $response = responseSuccess();
        } catch (Exception $e) {
            $response = responseError([
                'message' => $e->getMessage()
            ]);
        }

        return $response;
    }

    /**
     * Display the specified resource.
     */
    public function show(ContatoDentista $contatoDentista)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ContatoDentista $contatoDentista)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ContatoDentista $contatoDentista)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContatoDentista $contatoDentista)
    {
        //
    }
}
