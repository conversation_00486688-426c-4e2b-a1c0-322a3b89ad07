/* <PERSON><PERSON>trap Override - Otimização de espaço e elementos */

/* Redução geral de tamanhos */
.form-control, .input-group-text, .nav-link {
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
}

/* Inputs e formulários */
.form-control, .form-select {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
}

.form-label {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.input-group-text {
  padding: 0.25rem 0.5rem;
}

/* Botões */
/* .btn {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.btn-sm {
  padding: 0.15rem 0.5rem;
  font-size: 0.75rem;
} */

/* Cards e containers */
.card-body {
  padding: 0.75rem;
}

.card-header, .card-footer {
  padding: 0.5rem 0.75rem;
}

/* Melhorar responsividade em dispositivos móveis */
@media (max-width: 767.98px) {
  /* Permitir que inputs fiquem lado a lado em mobile */
  .row-compact-mobile > .col-6 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
  
  /* Reduzir margens e paddings em mobile */
  .container-fluid, .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  /* Ajustar tamanho de texto em mobile */
  body {
    font-size: 0.875rem;
  }
  
  .form-label {
    font-size: 0.8rem;
    margin-bottom: 0.15rem;
  }
}

/* Ajustes para tabelas */
.table th, .table td {
  padding: 0.4rem 0.5rem;
  font-size: 0.875rem;
}

/* Ajustes para modais */
.modal-header, .modal-footer {
  padding: 0.75rem;
}

.modal-body {
  padding: 0.75rem;
}

/* Ajustes para alertas e notificações */
.alert {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

/* Classe utilitária para aplicar em containers de formulários */
.form-compact .form-group {
  margin-bottom: 0.5rem;
}

/* Classe para aplicar em rows que precisam de inputs lado a lado em mobile */
.row-compact {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.row-compact > [class*="col-"] {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}




/* --- Input Group Container --- */
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap; /* Permite quebras de linha em casos específicos */
  align-items: stretch;
  width: 100%;
}