<template>
  <div class="calendar-datepicker-wrapper mx-auto">
    <div
      data-widget-item="calendar-datepicker"
      class="calendar-datepicker bg-white border w-full border-E0E0E0 rounded-lg flex justify-between flex-shrink flex-nowrap items-center relative selectDate select-none"
    >
      <!-- selected date -->
      <span class="date-display block p-2 text-center">
        {{ selectedDate ? displayDate : "YYYY-MM-AA" }}
      </span>
      <!--icon -->
      <font-awesome-icon
        icon="fa-solid fa-calendar"
        fixed-width
        class="calendar-icon text-base mr-3"
      ></font-awesome-icon>
      <!---->
      <input
        ref="dateinput"
        name="vue-pro-calendar-datepicker"
        id="vue-pro-calendar-datepicker"
        v-model="selectedDate"
        type="date"
        @change="emitDate"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
export interface Props {
  value?: Date;
}

import {
  ref,
  toRef,
  watch,
  computed,
  inject,
  onMounted,
  type ComponentPublicInstance,
} from "vue";
import type { Ref } from "vue";
import { dateLabel } from "./common";
import moment from 'moment';

// Set locale globally once to avoid repeated calls
moment.locale('pt-br');

type CalendarDateInput = HTMLInputElement & {
  showPicker(): void;
};

// const $t: any = inject("$t");
const props = withDefaults(defineProps<Props>(), {
  value: () => new Date(),
});
const emit = defineEmits(["changed"]);

const dateToString = (date: Date): string => {
  // Usar data local em vez de UTC para evitar problemas de fuso horário
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const selectedDate: Ref<string> = ref(dateToString(props.value));
const dateinput: Ref<ComponentPublicInstance<HTMLInputElement>> = ref<
  ComponentPublicInstance<HTMLInputElement>
>() as Ref<ComponentPublicInstance<HTMLInputElement>>;

const emitDate = (event: Event): void => {
  void event;
  if (!selectedDate.value) selectedDate.value = dateToString(new Date());

  // Criar uma data local a partir da string no formato YYYY-MM-DD
  const [year, month, day] = selectedDate.value.split('-').map(Number);
  const localDate = new Date(year, month - 1, day);

  emit("changed", localDate);
};

const displayDate = computed<string>((): string => {
  // Usar moment com a data local para garantir o fuso horário correto
  return moment(selectedDate.value, 'YYYY-MM-DD').format('dddd, D [de] MMMM [de] YYYY');
});

watch(props, () => {
  selectedDate.value = dateToString(props.value);
});

onMounted(() => {
  if ("showPicker" in HTMLInputElement.prototype) {
    dateinput.value.addEventListener("click", (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
      (event.target as CalendarDateInput).showPicker();
    });
  } else
    console.error("HTMLInputElement.prototype: `showPicker` not supported");
});

defineExpose({
  selectedDate,
});
</script>

<style scoped lang="scss">
.calendar-datepicker-wrapper {
  width: 100%;
  max-width: 600px;
  padding: 0.35rem 0 0.25rem;
}

.calendar-datepicker {
  height: calc(var(--lumi-input-height, 38px) + 6px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #E0E0E0;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #CCC;
  }
}

.date-display {
  color: #344767;
  font-weight: 600;
  font-size: 0.95rem;
  text-transform: capitalize;
  padding-left: 1rem !important;
  flex: 1;
}

.calendar-icon {
  color: #0ea5e9;
}

input[type="date"],
input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  box-sizing: border-box;
  cursor: pointer;
}

/* Estilização para centralizar o datepicker nativo */
:deep(::-webkit-datetime-edit) {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}

:deep(::-webkit-calendar-picker) {
  margin: 0 auto;
}
</style>
