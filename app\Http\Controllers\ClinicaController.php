<?php

namespace App\Http\Controllers;

use App\Models\Clinica;
use App\Models\Paciente;
use App\Traits\LogsActionHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ClinicaController extends Controller
{
    use LogsActionHistory;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $clinicas = Clinica::all();

        return response()->json($clinicas);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Gerar slug único baseado no nome
        $baseSlug = Str::slug(mb_strtolower(preg_replace('/[\s]/', '-', $request->nome)), '-');
        $slug = $this->generateUniqueSlug($baseSlug);

        $clinica = Clinica::create([
            'nome' => $request->nome,
            'slug' => $slug,
            'imagem_url' => $request->imagem_url,
            'endereco' => $request->endereco,
        ]);

        // Log the clinica creation action
        $this->logCreateAction($clinica, null, $request, "Created new clinica: {$clinica->nome}");

        return responseSuccess();
    }

    /**
     * Gera um slug único adicionando números se necessário
     */
    private function generateUniqueSlug($baseSlug)
    {
        $slug = $baseSlug;
        $counter = 2;

        // Verifica se o slug já existe
        while (Clinica::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Display the specified resource.
     */
    public function show(Clinica $clinica)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Clinica $clinica)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Clinica $clinica)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Clinica $clinica)
    {
        //
    }
}
