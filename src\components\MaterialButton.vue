<template>
  <button
    class="btn mb-0"
    :class="getClasses(variant, color, size, fullWidth, active, loading)"
    :disabled="loading || disabled"
  >
    <span v-if="loading" class="material-button-loading">
      {{ loadingText }}
    </span>
    <slot v-else />
  </button>
</template>

<script>
export default {
  name: "MaterialButton",
  props: {
    color: {
      type: String,
      default: "success",
    },
    size: {
      type: String,
      default: "md",
    },
    variant: {
      type: String,
      default: "fill",
    },
    fullWidth: {
      type: Boolean,
      default: false,
    },
    active: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    loadingText: {
      type: String,
      default: "Carregando...",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    getClasses: (variant, color, size, fullWidth, active, loading) => {
      let colorValue, sizeValue, fullWidthValue, activeValue, loadingValue;

      // Setting the button variant and color
      if (variant === "gradient") {
        colorValue = `bg-gradient-${color}`;
      } else if (variant === "outline") {
        colorValue = `btn-outline-${color}`;
      } else {
        colorValue = `btn-${color}`;
      }

      sizeValue = size ? `btn-${size}` : null;

      fullWidthValue = fullWidth ? `w-100` : null;

      activeValue = active ? `active` : null;

      loadingValue = loading ? `loading` : null;

      return `${colorValue} ${sizeValue} ${fullWidthValue} ${activeValue} ${loadingValue}`.trim();
    },
  },
};
</script>

<style scoped>
.btn.loading {
  position: relative;
  pointer-events: none;
}

.material-button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
}

.btn.loading:disabled {
  opacity: 0.85;
}
</style>
