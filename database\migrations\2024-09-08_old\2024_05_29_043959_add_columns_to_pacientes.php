<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pacientes', function (Blueprint $table) {
            $table->string('diagnostico');
            $table->string('tratamento');
            $table->date('data_inicio_tratamento')->nullable();
            $table->date('data_final_previsa')->nullable();
            $table->string('status_tratamento');
            $table->date('primeira_consulta')->nullable();
            $table->date('ultima_consulta')->nullable();
            $table->date('proxima_consulta')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    Schema::table('pacientes', function (Blueprint $table) {
        $table->dropColumn('diagnostico');
        $table->dropColumn('tratamento');
        $table->dropColumn('data_inicio_tratamento');
        $table->dropColumn('data_final_previsa');
        $table->dropColumn('status_tratamento');
        $table->dropColumn('primeira_consulta');
        $table->dropColumn('ultima_consulta');
        $table->dropColumn('proxima_consulta');
    });
    }
};
