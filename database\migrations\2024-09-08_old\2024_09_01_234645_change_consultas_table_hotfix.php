<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeConsultasTableHotfix extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('consultas', function (Blueprint $table) {
            // Adicionar novas colunas
            $table->string('paciente_nome')->nullable()->after('paciente_id');
            $table->unsignedBigInteger('convenio_id')->nullable()->after('dentista_id');
            $table->time('horario')->after('data');
            $table->float('valor')->nullable()->after('horario');
            $table->integer('reagendamentos')->default(0)->after('status');
            $table->text('notas')->nullable()->after('updated_at');
            $table->integer('cadastrado_por')->after('notas');

            // Alterar colunas existentes
            $table->dropColumn('data');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('consultas', function (Blueprint $table) {
            // Remover colunas adicionadas
            $table->dropColumn('paciente_nome');
            $table->dropColumn('convenio_id');
            $table->dropColumn('horario');
            $table->dropColumn('valor');
            $table->dropColumn('reagendamentos');
            $table->dropColumn('notas');
            $table->dropColumn('cadastrado_por');

            // Restaurar colunas alteradas
            $table->dateTime('data')->after('dentista_id');
        });
    }
}