<template>
  <lumi-sidenav
    icon="mdi-doctor"
    class="fixed-end lumi-sidenav"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>

  <div class="main-page-content">
    <div class="row">
      <div class="col-12">
        <div class="w-100 text-center mt-4">
          <input
            type="text"
            class="search-input"
            placeholder="Pesquisar..."
            @input="updateList($event.target.value)"
            v-model="search"
          />
        </div>

        <div v-if="isLoading.dentistasList" class="w-100 text-center py-5">
          <div class="spinner-border text-primary" role="status"></div>
        </div>

        <v-table v-if="!isLoading.dentistasList && dentistas.length == 0" class="m-3">
          <tbody>
            <tr>
              <td
                class="bg-gradient-light text-dark text-center"
                style="border-radius: 3px; padding: 2px 20px"
              >
                <span v-if="search == ''"
                  >Ainda não existem ortodontistas cadastrados.</span
                >
                <span v-if="search != ''">A busca não encontrou nenhum paciente.</span>
              </td>
            </tr>
          </tbody>
        </v-table>

        <EasyDataTable
          v-if="dentistas.length > 0"
          :headers="tableheaders"
          :items="dentistas"
          @click-row="openDentista"
          body-row-class-name="clickable"
          header-item-class-name="table-header-item"
          body-item-class-name="table-body-item"
          rowsPerPageMessage="Ortondontistas por página"
          rowsOfPageSeparatorMessage="de"
          emptyMessage="Sem resultados"
        >
          <template #header-nome="header">
            <div class="w-100 ps-3">ORTODONTISTA</div>
          </template>

          <template #item-nome="{ nome }">
            <div class="w-100 ps-3">
              <h6 class="mb-0 text-sm text-bold">{{ nome }}</h6>
            </div>
          </template>

          <template #header-pacientes="header">
            <div class="text-center w-100">PACIENTES</div>
          </template>

          <template #item-pacientes="{ pacientes }">
            <div class="align-middle text-center text-sm">
              {{ pacientes }}
            </div>
          </template>

          <template #item-status="{ status }">
            <div class="align-middle text-center text-sm">
              <span class="badge badge-sm w-100 w-md-70" :class="statusClass(status)">
                {{ statusText(status) }}
              </span>
            </div>
          </template>

          <template #header-pacientes_count="header">
            <div class="w-100 ps-3 text-center">PACIENTES</div>
          </template>

          <template #item-pacientes_count="{ pacientes_count }">
            <div class="w-100 text-center">
              <span class="badge bg-info mx-auto" style="font-size: 9pt">
                {{ pacientes_count }}
              </span>
            </div>
          </template>

          <template #item-created_at="{ created_at }">
            <p class="text-xs font-weight-bold mb-0">
              {{ $filters.dateTime(created_at) }}
            </p>
          </template>
        </EasyDataTable>
      </div>
    </div>
  </div>

  <div class="modal fade lumi-fade" tabindex="-1" id="modalNovoDentista" ref="modalNovoDentista">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 650px">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Novo ortodontista</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            ref="closeModalNovoDentista"
          ></button>
        </div>
        <div class="modal-body px-4">
          <div class="row">
            <div class="col-md-7">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'user']" /></span>
                Nome:
              </label>

              <MaterialInput
                type="text"
                v-model="novoDentista.nome"
                ref="nome"
                :isRequired="true"
                :input="
                  function ($event) {
                    onNomeInput($event);
                  }
                "
              />
            </div>

            <div class="col-md-5">
              <label>
                <span class="me-1"
                  ><font-awesome-icon :icon="['fas', 'hospital']"
                /></span>
                Clínica:
              </label>

              <select
                v-if="$user.system_admin && novoDentista.clinica_id !== 'add'"
                name=""
                id=""
                class="form-select"
                v-model="novoDentista.clinica_id"
                @change="changeClinica"
              >
                <option hidden selected value="">Selecionar...</option>
                <option v-for="clinica in clinicas" :key="clinica.id" :value="clinica.id">
                  {{ clinica.nome }}
                </option>
                <option value="add">Nova clínica...</option>
              </select>

              <MaterialInput
                v-if="$user.system_admin && novoDentista.clinica_id == 'add'"
                type="text"
                placeholder="Nome da nova clínica..."
                ref="novaClinica"
                :input="
                  function ($event) {
                    capitalizeAll($event);
                  }
                "
                v-model="novoDentista.novaClinica"
              />

              <input
                  v-if="!$user.system_admin"
                  readonly
                  class="form-control"
                  type="text"
                  :value="$clinica?.nome ? $clinica.nome : ''"
                />
            </div>

            <div class="col-md-7 mt-3">
              <label>
                <span class="me-1"
                  ><font-awesome-icon :icon="['fas', 'at']"
                /></span>
                Nome de usuário:
              </label>
              <MaterialInput type="text" v-model="novoDentista.username" :input="onUsernameInput" :isRequired="true" />
            </div>
            <div class="col-md-5 mt-3">
              <label>
                <span class="me-1">
                  <font-awesome-icon :icon="['fas', 'lock']" />
                </span>
                Senha:
              </label>
              <div class="d-flex">
                <MaterialInput
                  :type="showPassword ? 'text' : 'password'"
                  v-model="novoDentista.senha"
                  :isRequired="true"
                  class="flex-grow-1"
                  style="font-family: monospace; font-weight: 600; letter-spacing: 1px;"
                />
                <button type="button" class="btn btn-sm btn-outline-secondary" title="Exibir/Ocultar senha" @click="togglePasswordVisibility">
                  <font-awesome-icon :icon="['fas', showPassword ? 'eye-slash' : 'eye']" />
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" title="Gerar nova senha" @click="regeneratePassword">
                  <font-awesome-icon :icon="['fas', 'sync']" />
                </button>
              </div>
            </div>
            <div class="col-12 mt-3">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'bars']" /></span>
                Observações:
              </label>
              <textarea
                name=""
                id="novoDentista_observacoes"
                class="form-control"
                rows="5"
                v-model="novoDentista.observacoes"
              ></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" @click="confirmAddNovoDentista" :disabled="!isFormValid">
            Adicionar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cSwal from "@/utils/cSwal.js";
import MaterialInput from "@/components/MaterialInput.vue";
import { mapMutations, mapState, mapActions } from "vuex";
import DentistsTable from "./components/DentistsTable.vue";
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import { addNovoDentista, searchDentistas } from "@/services/dentistasService";
import { getClinicas } from "@/services/clinicasService";
import { capitalizeAll } from "@/helpers/utils.js";
import { closeModalWithAnimation } from "@/utils/modalHelper.js";

const tableheaders = [
  { text: "ORTODONTISTA", value: "nome", sortable: true },
  { text: "CLÍNICA", value: "clinica.nome", sortable: true },
  { text: "PACIENTES", value: "pacientes_count", sortable: true /*  align: 'center' */ },
  { text: "USUÁRIO", value: "user.username", sortable: true },
  { text: "CADASTRADO EM", value: "created_at", sortable: true },
];

var dentistas = [];

var search = "";

// Function to generate a random alphanumeric password with uppercase characters (6 characters)
function generateRandomPassword() {
  const chars = 'ABCDEFGHJKMNPQRTUVWXYZ123456789';
  let password = '';

  // Generate the rest of the characters (5 more to make it 6 total)
  for (let i = 0; i < 6; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  // Shuffle the password characters
  return password.split('').sort(() => 0.5 - Math.random()).join('');
}

function getNovoDentista() {
  return {
    nome: "",
    username: "",
    senha: generateRandomPassword(),
    novaClinica: "",
    clinica_id: "",
    usernameManuallyEdited: false,
  };
}

export default {
  name: "Dentistas",
  components: {
    MaterialInput,
    DentistsTable,
    LumiSidenav,
  },
  async created() {
    if (!this.$store?.state?.token?.system_admin) {
      this.$router.push({ name: "Agenda" });
    }
    this.refreshClinicas();
    this.updateList();
  },

  mounted() {
    // Configurar eventos do modal
    const modalElement = document.getElementById('modalNovoDentista');
    if (modalElement) {
      // Quando o modal for aberto, gerar senha padrão e focar no campo de nome
      modalElement.addEventListener("shown.bs.modal", (event) => {
        // Gerar senha padrão
        this.novoDentista.senha = generateRandomPassword();

        // Garantir que a senha esteja visível por padrão
        this.showPassword = true;

        // Focar no campo de nome
        if (this.$refs.nome) {
          this.$refs.nome.getInput().focus();
        }
      });

      // Adicionar classe para animação de fechamento
      modalElement.addEventListener('hide.bs.modal', () => {
        modalElement.classList.add('modal-closing');
      });

      // Remover a classe após o modal estar completamente fechado
      modalElement.addEventListener('hidden.bs.modal', () => {
        modalElement.classList.remove('modal-closing');
      });

      // Fechar sidenav quando o modal for aberto em telas pequenas
      modalElement.addEventListener('show.bs.modal', () => {
        // Verificar se estamos em uma tela pequena (< 992px - breakpoint md do Bootstrap)
        if (window.innerWidth < 992) {
          this.closeSidenav();
        }
      });
    }
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    // Método para fechar a sidenav
    closeSidenav() {
      // Verificar se a sidenav está aberta
      const sidenavElement = document.querySelector(".g-sidenav-show");
      if (sidenavElement && sidenavElement.classList.contains("g-sidenav-pinned")) {
        // Usar o método do Vuex para fechar a sidenav
        this.navbarMinimize();
      }
    },

    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      // Implementar as ações da sidenav
      switch (action) {
        case 'newDentist':
          // Modal já é aberto automaticamente pelos atributos data-bs-*
          break;
        case 'manageClinics':
          alert('Funcionalidade: Gerenciar clínicas');
          break;
      }
    },
    async refreshClinicas() {
      this.clinicas = await getClinicas();
    },
    capitalizeAll,
    changeClinica() {
      if (this.novoDentista.clinica_id == "add") {
        this.$refs.novaClinica.getInput().focus();
      }
    },

    removeAccents(str) {
      return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    },

    generateUsernameFromName(name) {
      if (!name) return '';
      const cleanedName = this.removeAccents(name).toLowerCase().trim();
      const parts = cleanedName.split(/\s+/);
      if (parts.length === 1) {
        return parts[0].replace(/[^a-z0-9]/g, '');
      } else {
        const first = parts[0].replace(/[^a-z0-9]/g, '');
        const last = parts[parts.length - 1].replace(/[^a-z0-9]/g, '');
        return `${first}.${last}`;
      }
    },

    onNomeInput(event) {
      this.capitalizeAll(event);
      if (!this.novoDentista.usernameManuallyEdited) {
        this.novoDentista.username = this.generateUsernameFromName(event.target.value);
      }
    },

    onUsernameInput(event) {
      if (event.target.value === '') {
        this.novoDentista.usernameManuallyEdited = false;
      } else {
        this.novoDentista.usernameManuallyEdited = true;
      }
    },

    // Method to regenerate a random password
    regeneratePassword() {
      this.novoDentista.senha = generateRandomPassword();
    },

    // Method to toggle password visibility
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },

    async updateList(search = "") {
      this.isLoading.dentistasList = true;
      this.dentistas = await searchDentistas(search);
      this.isLoading.dentistasList = false;
    },
    confirmAddNovoDentista() {
      cSwal.cConfirm("Deseja realmente adicionar este ortodontista?", async () => {
        cSwal.loading("Adicionando ortodontista...");
        const add = await addNovoDentista(this.novoDentista);
        cSwal.loaded();

        if (add) {
          cSwal.cSuccess("O ortodontista foi adicionado.");
          this.novoDentista = getNovoDentista();

          // Fechar o modal com animação
          closeModalWithAnimation('modalNovoDentista');

          // Fechar a sidenav
          this.closeSidenav();
        } else {
          cSwal.cError("Ocorreu um erro ao adicionar o ortodontista.");
        }

        await this.updateList(this.search);
      });
    },
    statusClass(status) {
      const classMap = {
        INATIVO: "bg-gradient-danger",
        ATIVO: "bg-gradient-success",
      };

      return classMap[status] || "";
    },
    statusText(status) {
      const textMap = {
        INATIVO: "INATIVO",
        ATIVO: "ATIVO",
      };

      return textMap[status] || "";
    },
    openDentista(dentista) {
      // Verificar se o ortodontista é de uma clínica diferente da que está logada
      const userClinica = this.$store?.state?.token?.clinica;
      const dentistaClinica = dentista.clinica;

      if (userClinica && dentistaClinica && userClinica.id !== dentistaClinica.id) {
        // Usar rota com slug da clínica se for de clínica diferente
        this.$router.push({
          name: "DentistaClinica",
          params: {
            clinica_slug: dentistaClinica.slug,
            id_matricula: dentista.id_matricula,
          },
        });
      } else {
        // Usar rota padrão se for da mesma clínica
        this.$router.push({
          name: "DentistaPadrao",
          params: {
            id_matricula: dentista.id_matricula,
          },
        });
      }
    },
  },
  data() {
    return {
      clinicas: [],
      isLoading: {
        dentistasList: true,
      },
      tableheaders,
      search,
      novoDentista: getNovoDentista(),
      dentistas,
      showPassword: true, // Senha exibida por padrão
      sidenavConfig: {
        groups: [
          {
            title: "ORTODONTISTAS",
            buttons: [
              {
                text: "Novo ortodontista",
                icon: ["fas", "plus"],
                iconType: "font-awesome",
                action: "newDentist",
                attributes: {
                  "data-bs-toggle": "modal",
                  "data-bs-target": "#modalNovoDentista"
                }
              }
            ]
          },
          {
            title: "CLÍNICAS",
            buttons: [
              {
                text: "Gerenciar clínicas",
                icon: ["fas", "cog"],
                iconType: "font-awesome",
                action: "manageClinics"
              }
            ]
          }
        ]
      }
    };
  },
  computed: {
    ...mapState([
      "isRTL",
      "color",
      "isAbsolute",
      "isNavFixed",
      "navbarFixed",
      "absolute",
      "showSidenav",
      "showNavbar",
      "showFooter",
      "showConfig",
      "hideConfigButton",
    ]),
    // Check if all required fields are filled
    isFormValid() {
      return this.novoDentista.nome &&
             this.novoDentista.username &&
             this.novoDentista.senha;
    }
  },
};
</script>
