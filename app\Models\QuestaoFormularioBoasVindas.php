<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class QuestaoFormularioBoasVindas extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'questoes_formulario_boas_vindas';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id_formulario',
        'input_id',
        'ordem',
        'obrigatoria',
        'questao',
        'tipo',
        'alternativas',
        'respostas',
        'detalhes',
        'ponto_positivo',
        'ponto_neutro',
        'ponto_negativo',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    // Add a relationship to the FormularioBoasVindas model
    public function formulario()
    {
        return $this->belongsTo(FormularioBoasVindas::class, 'id_formulario');
    }
}