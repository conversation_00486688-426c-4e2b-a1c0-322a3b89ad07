<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class RemoveColumnsFromQuestoesFormularioBoasVindasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('questoes_formulario_boas_vindas', function (Blueprint $table) {
            $table->dropColumn('pontos_positivos');
            $table->dropColumn('pontos_neutros');
            $table->dropColumn('pontos_negativos');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('questoes_formulario_boas_vindas', function (Blueprint $table) {
            $table->string('pontos_positivos')->nullable();
            $table->string('pontos_neutros')->nullable();
            $table->string('pontos_negativos')->nullable();
        });
    }
}