<?php

namespace Tests\Feature;

use App\Models\ActionHistory;
use App\Models\User;
use App\Models\Clinica;
use App\Models\Paciente;
use App\Services\ActionHistoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class ActionHistoryTest extends TestCase
{
    use RefreshDatabase;

    protected $actionHistoryService;
    protected $user;
    protected $clinica;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->actionHistoryService = app(ActionHistoryService::class);
        
        // Create test data
        $this->clinica = Clinica::factory()->create([
            'nome' => 'Test Clinic',
            'slug' => 'test-clinic'
        ]);
        
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'clinica_id' => $this->clinica->id
        ]);
    }

    public function test_can_log_create_action()
    {
        $paciente = new Paciente([
            'nome' => 'Test Patient',
            'clinica_id' => $this->clinica->id
        ]);
        $paciente->save();

        $newData = $paciente->toArray();

        $actionHistory = $this->actionHistoryService->logCreate(
            $this->user->id,
            $paciente,
            $newData,
            null,
            'Test create action'
        );

        $this->assertNotNull($actionHistory);
        $this->assertEquals(ActionHistory::ACTION_CREATE, $actionHistory->action_type);
        $this->assertEquals($this->user->id, $actionHistory->user_id);
        $this->assertEquals($paciente->id, $actionHistory->patient_id);
        $this->assertEquals($this->clinica->id, $actionHistory->clinica_id);
        $this->assertEquals('Test create action', $actionHistory->action_description);
        $this->assertEquals($newData, $actionHistory->new_data);
        $this->assertNull($actionHistory->old_data);
    }

    public function test_can_log_update_action()
    {
        $paciente = Paciente::factory()->create([
            'nome' => 'Original Name',
            'clinica_id' => $this->clinica->id
        ]);

        $oldData = $paciente->toArray();
        
        $paciente->nome = 'Updated Name';
        $paciente->save();
        
        $newData = $paciente->toArray();

        $actionHistory = $this->actionHistoryService->logUpdate(
            $this->user->id,
            $paciente,
            $oldData,
            $newData,
            null,
            'Test update action'
        );

        $this->assertNotNull($actionHistory);
        $this->assertEquals(ActionHistory::ACTION_UPDATE, $actionHistory->action_type);
        $this->assertEquals($this->user->id, $actionHistory->user_id);
        $this->assertEquals($paciente->id, $actionHistory->patient_id);
        $this->assertEquals('Test update action', $actionHistory->action_description);
        $this->assertEquals($oldData, $actionHistory->old_data);
        $this->assertEquals($newData, $actionHistory->new_data);
    }

    public function test_can_log_delete_action()
    {
        $paciente = Paciente::factory()->create([
            'nome' => 'Test Patient',
            'clinica_id' => $this->clinica->id
        ]);

        $oldData = $paciente->toArray();
        $pacienteId = $paciente->id;

        $actionHistory = $this->actionHistoryService->logDelete(
            $this->user->id,
            $paciente,
            $oldData,
            null,
            'Test delete action'
        );

        $this->assertNotNull($actionHistory);
        $this->assertEquals(ActionHistory::ACTION_DELETE, $actionHistory->action_type);
        $this->assertEquals($this->user->id, $actionHistory->user_id);
        $this->assertEquals($pacienteId, $actionHistory->patient_id);
        $this->assertEquals('Test delete action', $actionHistory->action_description);
        $this->assertEquals($oldData, $actionHistory->old_data);
        $this->assertNull($actionHistory->new_data);
    }

    public function test_action_history_model_relationships()
    {
        $paciente = Paciente::factory()->create([
            'clinica_id' => $this->clinica->id
        ]);

        $actionHistory = ActionHistory::create([
            'user_id' => $this->user->id,
            'action_type' => ActionHistory::ACTION_CREATE,
            'action_description' => 'Test action',
            'patient_id' => $paciente->id,
            'clinica_id' => $this->clinica->id,
            'entity_type' => get_class($paciente),
            'entity_id' => $paciente->id,
            'new_data' => ['test' => 'data'],
        ]);

        // Test relationships
        $this->assertEquals($this->user->id, $actionHistory->user->id);
        $this->assertEquals($paciente->id, $actionHistory->patient->id);
        $this->assertEquals($this->clinica->id, $actionHistory->clinica->id);
    }

    public function test_action_history_scopes()
    {
        $paciente = Paciente::factory()->create([
            'clinica_id' => $this->clinica->id
        ]);

        // Create multiple action histories
        ActionHistory::create([
            'user_id' => $this->user->id,
            'action_type' => ActionHistory::ACTION_CREATE,
            'action_description' => 'Create action',
            'patient_id' => $paciente->id,
            'clinica_id' => $this->clinica->id,
            'entity_type' => get_class($paciente),
            'entity_id' => $paciente->id,
        ]);

        ActionHistory::create([
            'user_id' => $this->user->id,
            'action_type' => ActionHistory::ACTION_UPDATE,
            'action_description' => 'Update action',
            'patient_id' => $paciente->id,
            'clinica_id' => $this->clinica->id,
            'entity_type' => get_class($paciente),
            'entity_id' => $paciente->id,
        ]);

        // Test scopes
        $createActions = ActionHistory::byActionType(ActionHistory::ACTION_CREATE)->get();
        $this->assertCount(1, $createActions);

        $userActions = ActionHistory::byUser($this->user->id)->get();
        $this->assertCount(2, $userActions);

        $patientActions = ActionHistory::byPatient($paciente->id)->get();
        $this->assertCount(2, $patientActions);

        $clinicaActions = ActionHistory::byClinica($this->clinica->id)->get();
        $this->assertCount(2, $clinicaActions);
    }

    public function test_data_sanitization()
    {
        $paciente = Paciente::factory()->create([
            'clinica_id' => $this->clinica->id
        ]);

        $dataWithSensitiveInfo = [
            'nome' => 'Test Patient',
            'password' => 'secret123',
            'password_confirmation' => 'secret123',
            'remember_token' => 'token123',
            'api_token' => 'api_token123',
            'normal_field' => 'normal_value'
        ];

        $actionHistory = $this->actionHistoryService->logCreate(
            $this->user->id,
            $paciente,
            $dataWithSensitiveInfo
        );

        $this->assertNotNull($actionHistory);
        $this->assertArrayNotHasKey('password', $actionHistory->new_data);
        $this->assertArrayNotHasKey('password_confirmation', $actionHistory->new_data);
        $this->assertArrayNotHasKey('remember_token', $actionHistory->new_data);
        $this->assertArrayNotHasKey('api_token', $actionHistory->new_data);
        $this->assertArrayHasKey('normal_field', $actionHistory->new_data);
        $this->assertEquals('normal_value', $actionHistory->new_data['normal_field']);
    }

    public function test_changes_summary()
    {
        $paciente = Paciente::factory()->create([
            'clinica_id' => $this->clinica->id
        ]);

        $oldData = ['nome' => 'Old Name', 'observacoes' => 'Old Notes'];
        $newData = ['nome' => 'New Name', 'observacoes' => 'Old Notes'];

        $actionHistory = ActionHistory::create([
            'user_id' => $this->user->id,
            'action_type' => ActionHistory::ACTION_UPDATE,
            'action_description' => 'Update action',
            'patient_id' => $paciente->id,
            'clinica_id' => $this->clinica->id,
            'entity_type' => get_class($paciente),
            'entity_id' => $paciente->id,
            'old_data' => $oldData,
            'new_data' => $newData,
        ]);

        $changes = $actionHistory->getChangesSummary();
        
        $this->assertArrayHasKey('nome', $changes);
        $this->assertEquals('Old Name', $changes['nome']['old']);
        $this->assertEquals('New Name', $changes['nome']['new']);
        $this->assertArrayNotHasKey('observacoes', $changes); // No change
        $this->assertTrue($actionHistory->hasDataChanges());
    }
}
