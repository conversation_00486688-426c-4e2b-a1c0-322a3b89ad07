<template>
  <div>
    <div class="row mt-2">
      <div class="col-12 mb-2 mt-2">
        <MaterialInput
          label="Nome"
          type="text"
          v-model="localData.responsavel_nome"
          :input="handleCapitalizeInput('responsavel_nome')"
          :id="'responsavel_nome' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
        />
      </div>
      <div class="col-7 col-sm-6 mb-2">
        <MaterialInput
          label="CPF"
          type="text"
          v-model="localData.responsavel_cpf"
          mask="###.###.###-##"
          :id="'responsavel_cpf' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('responsavel_cpf')"
        />
      </div>
      <div class="col-5 col-sm-6 mb-2">
        <MaterialInput
          label="RG"
          type="text"
          v-model="localData.responsavel_rg"
          :id="'responsavel_rg' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('responsavel_rg')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import { capitalizeAll } from "@/helpers/utils.js";

export default {
  name: "PatientResponsibleInfo",
  components: {
    MaterialInput
  },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    isEditing: {
      type: Boolean,
      default: false
    },
    isMobile: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localData: {
        responsavel_nome: this.paciente.responsavel_nome,
        responsavel_cpf: this.paciente.responsavel_cpf,
        responsavel_rg: this.paciente.responsavel_rg
      }
    };
  },
  watch: {
    paciente: {
      handler(newVal) {
        this.localData = {
          responsavel_nome: newVal.responsavel_nome,
          responsavel_cpf: newVal.responsavel_cpf,
          responsavel_rg: newVal.responsavel_rg
        };
      },
      deep: true
    }
  },
  methods: {
    capitalizeAll,
    updateField(field, value) {
      this.$emit('update:field', { field, value });
    },
    handleCapitalizeInput(fieldName) {
      return (event) => {
        if (event && event.target) {
          capitalizeAll(event);
          this.localData[fieldName] = event.target.value;
          this.updateField(fieldName, this.localData[fieldName]);
        }
      };
    },
    handleFieldInput(fieldName) {
      return (event) => {
        if (event && event.target) {
          this.localData[fieldName] = event.target.value;
          this.updateField(fieldName, this.localData[fieldName]);
        }
      };
    }
  }
};
</script>
