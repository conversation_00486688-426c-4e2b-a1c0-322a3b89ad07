<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $table = 'pacientes';

    public function up(): void
    {
        Schema::table($this->table, function (Blueprint $table) {
            $table->string('rg')->nullable();
            $table->string('nome_pai')->nullable();
            $table->string('nome_mae')->nullable();
            $table->string('responsavel_nome')->nullable();
            $table->string('responsavel_cpf')->nullable();
            $table->string('responsavel_rg')->nullable();
        });
    }
    
    public function down(): void
    {
        Schema::table($this->table, function (Blueprint $table) {
            $table->dropColumn('rg');
            $table->dropColumn('nome_pai');
            $table->dropColumn('nome_mae');
            $table->dropColumn('responsavel_nome');
            $table->dropColumn('responsavel_cpf');
            $table->dropColumn('responsavel_rg');
        });
    }
};