<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Atualizar todos os registros existentes para usar 'br' como idioma padrão
        DB::table('users')->where('language', 'pt-BR')->update(['language' => 'br']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Não é necessário reverter esta operação, pois não estamos alterando a estrutura da tabela
    }
};
