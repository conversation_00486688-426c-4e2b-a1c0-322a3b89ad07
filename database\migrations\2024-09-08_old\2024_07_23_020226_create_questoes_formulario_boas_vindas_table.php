<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateQuestoesFormularioBoasVindasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('questoes_formulario_boas_vindas', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('id_formulario');
            $table->foreign('id_formulario')->references('id')->on('formularios_boas_vindas');
            $table->string('questao');
            $table->string('tipo');
            $table->text('alternativas');
            $table->text('resposta');
            $table->text('detalhes');
            $table->text('ponto_positivo');
            $table->text('ponto_neutro');
            $table->text('ponto_negativo');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('questoes_formulario_boas_vindas');
    }
}