<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class FormularioBoasVindas extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'formularios_boas_vindas';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'horario',
        'id_paciente',
        'nome',
        'email',
        'whatsapp',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    // Add a HasMany relationship to QuestaoFormularioBoasVindas
    public function questoes()
    {
        return $this->hasMany(QuestaoFormularioBoasVindas::class, 'id_formulario', 'id');
    }
}
