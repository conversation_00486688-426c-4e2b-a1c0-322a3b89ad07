<template>
  <div class="page-width-container">
    <main class="page-width">
      <div class="container-fluid p-0">
        <div class="card no-shadow">
          <div class="card-body p-3">
              <div class="row gx-4 align-items-center flex-wrap">

                <div class="col-12 col-md-5 col-lg-6 d-flex align-items-center justify-content-between">
                  <div class="d-flex align-items-center">
                    <div class="avatar avatar-xl position-relative">
                      <input
                        id="profilePictureFileInput"
                        type="file"
                        accept="image/*"
                        @change="profilePicUpload"
                        hidden
                      />

                      <div class="profile-pic pointer" @click="confirmUpdatePhoto" title="Clique para alterar a foto de perfil">
                        <img
                            v-if="!isLoading.paciente"
                            :src="paciente.profile_picture_url"
                            alt="profile_image"
                            class="shadow-sm w-100 border-radius-lg"
                            @load="profilePictureLoaded"
                          />
                          <div v-else class="spinner-border text-primary" role="status"></div>
                      </div>
                    </div>

                    <div class="ms-3 patient-info">
                      <p class="mb-0 id-ficha" v-if="!isLoading.paciente && paciente.id_ficha !== undefined">#{{ String(paciente.id_ficha).padStart(3, '0') }}</p>
                      <p class="mb-0 id-ficha" v-else><span class="placeholder-text"># -</span></p>
                      <h5 class="mb-0 fs-4 patient-name pointer"
   :class="{'long-name': paciente.nome && paciente.nome.length > 35}"
   @click="confirmChangeName"
   title="Clique para alterar o nome">
   {{ paciente.nome }}
   <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon-hover ms-1" style="font-size: 0.7em;" />
</h5>
                      <p class="mb-0 font-weight-bold">
                        <Transition>
                          <span :key="paciente.data_nascimento">{{
                            $filters.howMuchTime(paciente.data_nascimento, {
                              type: "date",
                              prefix: false,
                            })
                          }}</span>
                        </Transition>
                      </p>
                    </div>
                  </div>
                  <BackButton
                    class="back-btn"
                    :class="{ 'edit-mode': editModeActive }"
                  />
                  <!-- Debug: {{ editModeActive ? 'Em modo de edição' : 'Não está em modo de edição' }} -->
                </div>

                <div class="col-12 col-md-7 col-lg-6 mt-3 mt-lg-0 p-0">
                  <!-- Menu para telas pequenas (sm e abaixo) -->
                  <div class="nav-wrapper position-relative end-0 px-md-2 px-sm-0">
                    <ul class="p-1 bg-transparent nav nav-pills nav-fill menu-2x2" role="tablist">
                      <li class="nav-item perfil" @click="openTab('perfil')">
                        <a
                          class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                          :class="{ active: activeTab === 'perfil' }"
                          href="javascript:;"
                          role="tab"
                          :aria-selected="activeTab === 'perfil' ? 'true' : 'false'"
                        >
                          <i class="fas fa-user"></i>
                          <span class="mt-1">Perfil</span>
                        </a>
                      </li>
                      <li class="nav-item" @click="openTab('planejamento')">
                        <a
                          class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                          :class="{ active: activeTab === 'planejamento' }"
                          href="javascript:;"
                          role="tab"
                          :aria-selected="activeTab === 'planejamento' ? 'true' : 'false'"
                        >
                          <i class="fas fa-search"></i>
                          <span class="mt-1">Planejamento</span>
                        </a>
                      </li>
                      <li class="nav-item tratamento" @click="openTab('tratamento')">
                        <a
                          class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                          :class="{ active: activeTab === 'tratamento' }"
                          href="javascript:;"
                          role="tab"
                          :aria-selected="activeTab === 'tratamento' ? 'true' : 'false'"
                        >
                          <i class="fas fa-teeth-open"></i>
                          <span class="mt-1">Tratamento</span>
                        </a>
                      </li>
                      <li class="nav-item financeiro" @click="openTab('financeiro')">
                        <a
                          class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                          :class="{ active: activeTab === 'financeiro' }"
                          href="javascript:;"
                          role="tab"
                          :aria-selected="activeTab === 'financeiro' ? 'true' : 'false'"
                        >
                          <i class="fas fa-dollar-sign"></i>
                          <span class="mt-1">Financeiro</span>
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>

              </div>
          </div>
        </div>
      </div>
      <Transition>
        <div class="p-0 container-fluid" v-if="activeTab == 'perfil'">
          <div class="row">
            <div class="col-md-12">
              <Transition>
                <div class="main-container" v-if="activeProfileTab == 'perfilPessoal'">
                  <!-- Mobile Accordion View (sm and below) -->
                  <div class="d-block d-md-none mb-4">
                    <PatientAccordion
                      :paciente="paciente"
                      :isFilledPessoal="isFilledPessoal"
                      :isEditingPessoal="isEditingPessoal"
                      :isFilledResponsavel="isFilledResponsavel"
                      :isEditingResponsavel="isEditingResponsavel"
                      :isFilledEndereco="isFilledEndereco"
                      :isEditingEndereco="isEditingEndereco"
                      :isEditingMeiosContatos="isEditing.meiosContatos"
                      :dentistas="dentistas"
                      :clinicas="clinicas"
                      :isSystemAdmin="$user.system_admin"
                      :novoContato="novoContato"
                      :getContatoPlaceholder="getContatoPlaceholder"
                      :hasPendingChanges="hasPendingChanges"
                      :possuiWhatsapp="possuiWhatsapp"
                      v-model:editModeActive="editModeActive"
                      @toggle-edit-pessoal="toggleEditPessoal"
                      @toggle-edit-responsavel="toggleEditResponsavel"
                      @toggle-edit-endereco="toggleEditEndereco"
                      @toggle-edit-mode="toggleEditMode"
                      @select-meio-contato="selectMeioContato"
                      @contato-change="contatoChange"
                      @adicionar-contato="adicionarContato"
                      @excluir-contato="excluirContato"
                      @get-endereco="getEndereco"
                      @update:field="updatePacienteField"
                      @save-changes="confirmSavePaciente"
                      @edit-mode-change="handleEditModeChange"
                      @handle-form-link-btn="handleFormLinkBtn"
                      @toggle-formulario-view="toggleFormularioView"
                      @iniciar-diagnostico-planejamento="iniciarDiagnosticoPlanejamento"
                    />
                  </div>

                  <!-- Desktop View (md and above) -->
                  <div class="d-none d-md-flex row g-0 mb-4">
                    <div class="col-md-6 border-end-md px-4">
                      <div class="section-header">
                        <div class="section-header-content">
                          <div class="section-icon">
                            <font-awesome-icon :icon="['fas', 'user']" />
                          </div>
                          <p class="text-uppercase mb-0">Informações pessoais</p>
                        </div>
                        <div class="section-actions">
                          <span
                            v-if="isFilledPessoal && !isEditingPessoal"
                            class="edit-icon-wrapper-light"
                            title="Editar informações pessoais"
                            @click="toggleEditPessoal">
                            <font-awesome-icon
                              :icon="['fas', 'edit']"
                              class="edit-icon-light"
                            />
                          </span>
                          <span
                            v-if="isEditingPessoal && isFilledPessoal"
                            class="cancel-edit-text text-info pointer ms-2"
                            @click="toggleEditPessoal"
                            ><u>Cancelar edição</u></span
                          >
                        </div>
                      </div>

                      <PatientPersonalInfo
                        :paciente="paciente"
                        :isEditing="isEditingPessoal"
                        :isMobile="false"
                        :dentistas="dentistas"
                        :clinicas="clinicas"
                        :isSystemAdmin="$user.system_admin"
                        :showResponsibleInfo="true"
                        @update:field="updatePacienteField"
                      />

                      <div class="p-horizontal-divider my-3"></div>
                      <div class="col-12">
                        <div class="section-header">
                          <div class="section-header-content">
                            <div class="section-icon">
                              <font-awesome-icon :icon="['fas', 'users']" />
                            </div>
                            <p class="text-uppercase mb-0">Informações do responsável</p>
                          </div>
                          <div class="section-actions">
                            <span
                              v-if="isFilledResponsavel && !isEditingResponsavel"
                              class="edit-icon-wrapper-light"
                              title="Editar informações do responsável"
                              @click="toggleEditResponsavel">
                              <font-awesome-icon
                                :icon="['fas', 'edit']"
                                class="edit-icon-light"
                              />
                            </span>
                            <span
                              v-if="isEditingResponsavel && isFilledResponsavel"
                              class="cancel-edit-text text-info pointer ms-2"
                              @click="toggleEditResponsavel"
                              ><u>Cancelar edição</u></span
                            >
                          </div>
                        </div>
                      </div>

                      <PatientResponsibleInfo
                        :paciente="paciente"
                        :isEditing="isEditingResponsavel"
                        :isMobile="false"
                        @update:field="updatePacienteField"
                      />

                      <div class="p-horizontal-divider"></div>
                      <div class="col-12">
                        <div class="section-header">
                          <div class="section-header-content">
                            <div class="section-icon">
                              <font-awesome-icon :icon="['fas', 'comment']" />
                            </div>
                            <p class="text-uppercase mb-0">Observações</p>
                          </div>
                        </div>
                        <textarea
                          class="form-control mt-3"
                          id="paciente_observacoes"
                          rows="3"
                          v-model="paciente.observacoes"
                        >
                        </textarea>
                      </div>
                    </div>

                    <div class="col-12 col-md-6 px-4">
                      <hr class="horizontal dark" />
                      <div class="section-header">
                        <div class="section-header-content">
                          <div class="section-icon">
                            <font-awesome-icon :icon="['fas', 'address-book']" />
                          </div>
                          <p class="text-uppercase mb-0">Meios de contato</p>
                        </div>
                        <div class="section-actions">
                          <span
                            v-if="!isEditing.meiosContatos && paciente?.contatos?.length > 0"
                            class="edit-icon-wrapper-light"
                            title="Gerenciar meios de contato"
                            @click="toggleEditMode('meiosContatos')">
                            <font-awesome-icon
                              :icon="['fas', 'edit']"
                              class="edit-icon-light"
                            />
                          </span>
                          <span
                            v-if="isEditing.meiosContatos"
                            class="cancel-edit-text text-info pointer ms-2"
                            @click="toggleEditMode('meiosContatos')"
                            ><u>Cancelar edição</u></span
                          >
                        </div>
                      </div>

                      <PatientContactInfo
                        :paciente="paciente"
                        :isEditing="isEditing.meiosContatos"
                        :isMobile="false"
                        :novoContato="novoContato"
                        :novoContatoMask="novoContatoMask"
                        :getContatoPlaceholder="getContatoPlaceholder"
                        @select-meio-contato="selectMeioContato"
                        @contato-change="contatoChange"
                        @adicionar-contato="adicionarContato"
                        @excluir-contato="excluirContato"
                        @update:field="updatePacienteField"
                      />

                      <div class="p-horizontal-divider mb-0 w-100"></div>
                      <div class="section-header">
                        <div class="section-header-content">
                          <div class="section-icon">
                            <font-awesome-icon :icon="['fas', 'map-marker-alt']" />
                          </div>
                          <p class="text-uppercase mb-0">Endereço</p>
                        </div>
                        <div class="section-actions">
                          <span
                            v-if="isFilledEndereco && !isEditingEndereco"
                            class="edit-icon-wrapper-light"
                            title="Editar endereço"
                            @click="toggleEditEndereco">
                            <font-awesome-icon
                              :icon="['fas', 'edit']"
                              class="edit-icon-light"
                            />
                          </span>
                          <span
                            v-if="isEditingEndereco && isFilledEndereco"
                            class="cancel-edit-text text-info pointer ms-2"
                            @click="toggleEditEndereco"
                            ><u>Cancelar edição</u></span
                          >
                        </div>
                      </div>

                      <PatientAddressInfo
                        :paciente="paciente"
                        :isEditing="isEditingEndereco"
                        :isMobile="false"
                        @get-endereco="getEndereco"
                        @update:field="updatePacienteField"
                      />
                      <div class="p-horizontal-divider w-100"></div>
                      <div class="section-header ficha-header">
                        <div class="section-header-content">
                          <div class="section-icon">
                            <font-awesome-icon :icon="['fas', 'clipboard-check']" />
                          </div>
                          <p class="text-uppercase mb-0">Ficha de avaliação inicial</p>
                          <span v-if="paciente.formulario_respondido" class="badge badge-sm bg-success me-2">Respondida</span>
                          <span v-else class="badge badge-sm bg-warning me-2">Não respondida</span>
                        </div>
                        <div class="section-actions" v-if="!isLoading.paciente">
                          <button
                            v-if="!paciente.formulario_respondido"
                            class="btn btn-sm btn-primary mb-0 action-button"
                            @click="handleFormLinkBtn"
                          >
                            <font-awesome-icon
                              :icon="possuiWhatsapp ? ['fab', 'whatsapp'] : ['fas', 'copy']"
                              class="me-2"
                            />
                            <span>{{ possuiWhatsapp ? "ENVIAR LINK" : "COPIAR LINK" }}</span>
                          </button>
                          <button
                            v-else
                            class="btn btn-sm btn-primary mb-0 action-button"
                            @click="toggleFormularioView"
                            data-bs-toggle="modal"
                            data-bs-target="#modalFormularioView"
                          >
                            <font-awesome-icon :icon="['fas', 'eye']" class="me-2" />
                            <span>VISUALIZAR</span>
                          </button>
                        </div>
                      </div>
                      <div class="p-horizontal-divider w-100"></div>
                      <div class="next-btn-container py-2 py-md-3 mt-3">
                        <button
                          class="btn btn-success mb-0"
                          @click="iniciarDiagnosticoPlanejamento"
                        >
                          <i class="me-2 fas fa-play" style="font-size: 13pt"></i>
                          <span class="uppercase" style="font-size: 10pt">
                            Iniciar diagnóstico e planejamento
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>

                  <Transition name="fadeHeight">
                    <div
                      v-cloak
                      v-if="hasPendingChanges && !isLoading.paciente"
                      class="row col-12 d-none d-md-block"
                    >
                      <div class="p-horizontal-divider my-0"></div>
                      <div class="w-100 py-3 text-center">
                        <button
                          class="btn btn btn-primary m-0"
                          @click="confirmSavePaciente"
                        >
                          Salvar alterações
                        </button>
                      </div>
                    </div>
                  </Transition>

                  <div class="row mb-5 mt-4 mx-0">
                    <div class="col-12">
                      <div class="patient-details-container">
                        <div class="patient-details-header text-center mb-0">
                          <h5 class="mb-0">Detalhes do paciente</h5>
                        </div>

                        <div class="patient-details-content">
                          <div v-if="isLoading.paciente" class="w-100 text-center py-5">
                            <div class="spinner-border text-primary" role="status"></div>
                          </div>

                          <div v-if="!isLoading.paciente">
                            <div
                              v-if="
                                !paciente.formulario_respondido ||
                                detalhesPessoais.length == 0
                              "
                              class="empty-state-message"
                            >
                              <div class="icon-wrapper">
                                <font-awesome-icon
                                  :icon="['fas', 'clipboard-list']"
                                  class="empty-state-icon"
                                />
                              </div>
                              <p>
                                O paciente ainda não respondeu à ficha de avaliação inicial.
                                Para enviar-lhe o formulário, utilize o botão
                                "<font-awesome-icon
                                  :icon="
                                    possuiWhatsapp
                                      ? ['fab', 'fa-whatsapp']
                                      : ['fas', 'fa-copy']
                                  "
                                  class="me-1"
                                /><span class="font-weight-bold">{{
                                  possuiWhatsapp ? "ENVIAR LINK" : "COPIAR LINK"
                                }}</span
                                >" acima.
                              </p>
                            </div>
                            <div v-if="paciente.formulario_respondido" class="row g-3">
                              <div
                                v-for="(detalhe, index) in detalhesPessoais"
                                v-bind:key="index"
                                class="col-sm-6 col-md-4"
                              >
                                <div class="patient-info-item" :class="detalhe.nivel">
                                  <div class="info-icon">
                                    <font-awesome-icon
                                      :icon="['fas', getInfoIcon(detalhe.nivel)]"
                                    />
                                  </div>
                                  <div class="info-content">
                                    <span>{{ detalhe.detalhe }}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Botão de exclusão centralizado -->
                      <div class="text-center">
                        <button
                          class="delete-patient-btn"
                          @click="confirmarExcluirPaciente"
                          title="Excluir paciente"
                        >
                          <font-awesome-icon :icon="['fas', 'trash-alt']" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </Transition>
              <Transition>
                <div v-if="activeProfileTab == 'perfilClinico'"></div>
              </Transition>
            </div>
            <!-- <div class="col-md-4">
          <profile-card />
        </div> -->
          </div>
        </div>
      </Transition>

      <Transition>
        <Planejamento
          v-if="activeTab == 'planejamento'"
          :paciente="paciente"
          @pacienteChange="refreshPaciente"
          @edit-mode-active="handleEditModeActive"
        />
      </Transition>

      <Transition>
        <Tratamento
          v-if="activeTab == 'tratamento'"
          :paciente="paciente"
          :dentistas="dentistas"
          @update:consultas="updateConsultas"
          @editar-consulta="editarConsulta"
          @ver-historico="verHistoricoConsulta"
          @pacienteChange="refreshPaciente"
        />
      </Transition>

      <Transition>
        <div class="py-4 container-fluid" v-if="activeTab == 'financeiro'">
          <div class="row">
            <div class="col-md-12">
              <v-table>
                <tbody>
                  <tr>
                    <td
                      class="bg-gradient-success text-light text-center"
                      style="border-radius: 3px; padding: 2px 20px"
                    >
                      Não há pendências financeiras.
                    </td>
                  </tr>
                </tbody>
              </v-table>
            </div>
            <!-- <div class="col-md-4">
          <profile-card />
        </div> -->
          </div>
        </div>
      </Transition>
    </main>

    <div class="modal fade" tabindex="-1" id="modalFormularioView">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Ficha de avaliação inicial</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body p-3 px-4" style="max-height: 78vh; overflow-y: auto">
            <div class="d-flex flex-column align-center justify-content-center mb-0 pb-0">
              <label for=""><b>Respondida em:</b></label>
              <span>{{ $filters.dateTime(dataRespostaFicha) }}</span>
            </div>

            <div class="p-horizontal-divider"></div>

            <div style="max-width: 400px; margin: 0 auto">
              <div
                v-for="(question, index) in questoesFichaInicial"
                :key="index"
                class="mt-2 mb-4"
                :ref="'question' + index"
              >
                <label
                  v-if="
                    question.tipo !== 'text' &&
                    question.tipo !== 'date' &&
                    question.tipo !== 'phone' &&
                    question.tipo !== 'email'
                  "
                  class="mb-3 p-0 font-weight-bolder label-highlight"
                  >{{ question.questao }}
                  <span v-if="question.obrigatoria" class="text-danger">*</span>
                </label>

                <div
                  v-if="
                    question.tipo === 'text' ||
                    question.tipo === 'date' ||
                    question.tipo === 'phone' ||
                    question.tipo === 'email'
                  "
                  class="mt-0 p-0"
                >
                  <MaterialInput
                    :readonly="true"
                    :type="question.tipo === 'phone' ? 'text' : question.tipo"
                    :name="question.id"
                    :id="question.id"
                    :ref="question.id"
                    :label="question.questao"
                    labelClass="font-weight-bolder label-highlight"
                    v-model="question.resposta"
                    :required="question.obrigatoria"
                    :input="
                      function ($event) {
                        textInputEvent($event, question);
                      }
                    "
                    :placeholder="question.tipo === 'phone' ? '(##) #####-####' : null"
                    :style="
                      question.textOptions && question.textOptions.includes('center')
                        ? 'text-align: center !important'
                        : ''
                    "
                  />
                </div>

                <div v-else-if="question.tipo === 'checkbox'" class="px-3">
                  <table class="options-checkbox">
                    <tr
                      v-for="(alternativa, alternativaIndex) in question.alternativas"
                      :key="alternativaIndex"
                    >
                      <td class="d-flex flex-row align-center">
                        <input
                          type="checkbox"
                          class="form-checkbox"
                          :name="question.id + '-' + alternativa.resposta"
                          :id="question.id + '-' + alternativa.resposta"
                          :checked="alternativa.selecionada"
                          @click.prevent
                        />
                        <label
                          :for="question.id + '-' + alternativa.resposta"
                          style="padding-top: 5px"
                          >{{ alternativa.resposta }}</label
                        >
                      </td>
                    </tr>
                  </table>
                </div>

                <div v-else-if="question.tipo === 'radio'" class="row px-3">
                  <div
                    v-for="(alternativa, alternativaIndex) in question.alternativas"
                    v-bind:key="alternativaIndex"
                    class="col-6"
                    style="text-align: left"
                    :class="{
                      'ps-6': question.alternativas.length == 2 && alternativaIndex == 0,
                    }"
                  >
                    <input
                      type="radio"
                      class="form-radio"
                      :name="question.id"
                      :id="`alternativa-${question.id}-${alternativaIndex}`"
                      :value="alternativa.resposta"
                      v-model="question.resposta"
                      @click.prevent
                    />
                    <label
                      :for="`alternativa-${question.id}-${alternativaIndex}`"
                      class="radio-label"
                    >
                      {{ alternativa.resposta }}</label
                    >
                  </div>
                </div>

                <div
                  v-if="question.detalhar && question.detalhar === 'opcional'"
                  class="d-flex flex-row align-center justify-content-center"
                >
                  <input
                    type="checkbox"
                    class="form-checkbox"
                    :name="question.id + '-detalhar-cb'"
                    :id="question.id + '-detalhar-cb'"
                    v-model="question.detalhando"
                    @click.prevent
                  />
                  <label
                    :for="question.id + '-detalhar-cb'"
                    class="label-big"
                    style="padding-top: 8px"
                  >
                    {{
                      question.titulo_questao_detalhe
                        ? question.titulo_questao_detalhe
                        : "Detalhar..."
                    }}
                  </label>
                </div>

                <!-- Caso a questão tiver detalhamento obrigatório ou o detalhamento for optado pelo usuário -->
                <div v-if="question.detalhes">
                  <MaterialInput
                    :readonly="true"
                    :name="question.id + '-detalhar'"
                    label="Detalhes"
                    labelClass="label-big"
                    :id="question.id + '-detalhar'"
                    v-model="question.detalhes"
                  />
                </div>
                <!-- Exibe o divider, exceto no último elemento -->
                <div
                  v-if="index !== questoesFichaInicial.length - 1"
                  class="p-horizontal-divider primary"
                ></div>
              </div>
            </div>
            <!-- v-for / -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-bs-dismiss="modal">
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  <div v-if="isLoading.paciente" class="full-screen-spinner">
    <div class="spinner-border text-primary" role="status" style="width: 5rem; height: 5rem;"></div>
  </div>
</div>
</template>

<style>
/* button.lumi-fab {
  display: none !important;
} */
</style>

<style scoped>

.avatar {
  width: 80px !important;
  height: 80px !important;
}

.profile-pic {
  height: 80px;
  width: 80px;
  padding: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 12px; /* rounded corners for the frame */
  box-shadow: 0 0 0 4px #f0f2f5; /* subtle beige almost white frame */
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
}

/* Patient info styles */
.patient-info {
  min-width: 0; /* Enable text truncation */
  max-width: calc(100% - 40px); /* Leave space for back button */
}

.id-ficha {
  font-size: 1.2rem;
  color: #6c757d;
  line-height: 1.1;
  margin-bottom: 2px;
  font-weight: 600;
}

.placeholder-text {
  opacity: 0.5;
}

.patient-name {
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin-bottom: 2px;
}

.long-name {
  font-size: 1.1rem !important; /* Smaller font for long names */
}

.patient-name {
  transition: all 0.2s ease;
  position: relative;
}

.patient-name:hover {
  color: #3a8bd6;
}

.patient-name .edit-icon-hover {
  transition: all 0.2s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-5px);
  color: inherit; /* Herda a cor do texto pai */
}

.patient-name:hover .edit-icon-hover {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  color: #3a8bd6; /* Mesma cor do texto em hover */
}

/* Ajustes para telas mobile */
@media (max-width: 767.98px) {
  .id-ficha {
    font-size: 1.1rem !important;
  }

  .patient-name {
    font-size: 1.15rem !important;
  }

  .long-name {
    font-size: 1rem !important;
  }

  .patient-details-container {
    margin-top: 1.5rem !important;
  }
}

/* Back button styles */
.back-btn {
  flex-shrink: 0;
  margin-left: 15px;
  z-index: 5; /* Ensure button stays above other content */
  display: flex;
  align-items: center;
}

/* No need to hide desktop back button in edit mode */
/* The FAB button is hidden via v-if in the component */

.profile-pic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(58, 139, 214, 0.4);
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-pic:hover::before {
  opacity: 1;
}

.profile-pic::after {
  content: '\f030'; /* FontAwesome camera icon */
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 1.5rem;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 3;
  pointer-events: none;
}

.profile-pic:hover::after {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

/* Tooltip para indicar ação */

.profile-pic:hover {
  box-shadow: 0 0 0 4px #3a8bd6, 0 8px 25px rgba(58, 139, 214, 0.3); /* blue frame + shadow on hover */
  transform: scale(1.02);
}

.profile-pic:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.profile-pic img {
  height: 100%;
  width: 100%;
  object-fit: cover;
  border-radius: 8px; /* slightly smaller radius to show frame */
  border: 2px solid white; /* white border between image and frame */
  position: relative;
  z-index: 1;
  transition: transform 0.3s ease;
}

.full-screen-spinner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050; /* above most elements */
}

/* Patient Details Section Styles */
.patient-details-container {
  border-top: 1px solid #eee;
  background: linear-gradient(to bottom, #f8fbff, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.patient-details-header {
  background: linear-gradient(135deg, #3a8bd6, #5a9bd5);
  padding: 1rem;
  position: relative;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

.patient-details-header h5 {
  color: white;
  font-weight: 500;
  font-size: 1.25rem;
  position: relative;
  z-index: 2;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.patient-details-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  z-index: 1;
}

.patient-details-content {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0 0 12px 12px;
}

/* Empty state styling */
.empty-state-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  text-align: center;
  background-color: #f8fafc;
  border-radius: 8px;
  margin: 0.5rem 0;
  border: 1px dashed #d1dce8;
}

.empty-state-message .icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e6f2ff, #d1e6ff);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.empty-state-message .empty-state-icon {
  font-size: 1.5rem;
  color: #5a9bd5;
}

.empty-state-message p {
  color: #5c6b7a;
  font-size: 1rem;
  line-height: 1.5;
  max-width: 500px;
  margin: 0 auto;
}

/* Patient info items styling */
.patient-info-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 15px;
  border-radius: 8px;
  background-color: #f9fafc;
  border-left: 4px solid #ddd;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  height: 100%;
}

.patient-info-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.patient-info-item.positivo {
  border-left-color: #4CAF50;
  background-color: #f1f8f1;
}

.patient-info-item.neutro {
  border-left-color: #80A1BB;
  background-color: #f5f9fc;
}

.patient-info-item.atencao {
  border-left-color: #FFC107;
  background-color: #fffbf0;
}

.patient-info-item.negativo {
  border-left-color: #F44336;
  background-color: #fef5f4;
}

.patient-info-item .info-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 12px;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.patient-info-item.positivo .info-icon {
  color: #4CAF50;
}

.patient-info-item.neutro .info-icon {
  color: #80A1BB;
}

.patient-info-item.atencao .info-icon {
  color: #FFC107;
}

/* Estilos para o botão de exclusão de paciente */
.delete-patient-btn {
  width: 42px;
  height: 42px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc3545;
  border: 1px solid #dc3545;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(220, 53, 69, 0.3);
  padding: 0;
  margin: 1.5rem auto 0;
  font-size: 1rem;
}

.delete-patient-btn:hover {
  background-color: #dc3545;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.patient-info-item.negativo .info-icon {
  color: #F44336;
}

.patient-info-item .info-content {
  flex-grow: 1;
  font-size: 0.95rem;
  line-height: 1.4;
  padding-top: 2px;
}

/* Section Headers Styling */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 6px;
  margin: 15px 0 10px;
  border-radius: 8px;
  background: linear-gradient(to right, #f8f9fa, #f1f3f5);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #5a9bd5;
  transition: all 0.2s ease;
  flex-wrap: nowrap;
  flex-direction: row;
}

.section-header:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.section-header-content {
  display: flex;
  align-items: center;
  max-width: calc(100% - 80px);
  overflow: hidden;
  flex-shrink: 1;
  flex-direction: row;
  flex-wrap: wrap;
}

.section-icon {
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 0;
  background-color: rgba(90, 155, 213, 0.1);
  color: #5a9bd5;
  flex-shrink: 0;
}

.section-icon svg {
  font-size: 14pt;
}

.section-header p {
  font-weight: 600;
  font-size: 0.8rem;
  color: #495057;
  letter-spacing: 0.3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
}

.section-header-content .badge {
  margin-left: 8px;
  align-self: center;
}

.section-header-content span {
  display: inline-flex;
  align-items: center;
}

.section-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  min-width: 70px;
  justify-content: center;
}

.section-actions .badge {
  padding: 0.4em 0.6em;
  font-size: 0.7rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  border-radius: 4px;
  text-transform: uppercase;
}

.section-actions .action-button {
  font-size: 0.7rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  padding: 0.6em 1.4em;
  border-radius: 6px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.section-actions .action-button svg {
  font-size: 1.7em;
}

.section-actions .action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.ficha-header {
  width: 100%;
  margin-top: 0;
}

.cancel-edit-text {
  font-size: 9pt;
}

.pointer {
  cursor: pointer;
}

/* Estilos para o ícone de edição em fundo branco */
.edit-icon-wrapper-light {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-icon-wrapper-light:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.03);
}

.edit-icon-light {
  color: #495057;
  font-size: 14px;
}

/* Navbar horizontal em todas as telas */
.menu-2x2 {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: space-between !important;
  width: 100%;
}

.menu-2x2 .nav-item {
  flex: 1;
  text-align: center;
}

.nav-tab {
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* Ajustes para telas pequenas */
@media (max-width: 767.98px) {
  .menu-2x2 {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 0.25rem !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .menu-2x2 .nav-item {
    margin: 0 3px;
  }

  .menu-2x2 .nav-item .nav-link {
    padding: 0.5rem 0.25rem !important;
    font-size: 0.85rem;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    background-color: white;
  }

  .menu-2x2 .nav-item .nav-link.active {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .menu-2x2 .nav-item .nav-link i {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
  }

  .menu-2x2 .nav-item .nav-link span {
    font-size: 0.7rem;
    font-weight: 500;
  }

  /* Mobile accordion styles */
  .patient-accordion .accordion-button {
    padding: 0.75rem 1rem;
  }

  .patient-accordion .accordion-body {
    padding: 1rem;
  }

  .patient-accordion .accordion-item {
    margin-bottom: 0.75rem;
  }
}

/* Desktop layout fixes */
@media (min-width: 768px) {
  .row.g-0 {
    display: flex;
    flex-wrap: wrap;
  }

  .border-end-md {
    border-right: 1px solid #dee2e6;
  }
}

/* Modal fade animation */
.modal.fade {
  transition: opacity 0.3s ease-in-out;
}

.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -25px);
}

.modal.show .modal-dialog {
  transform: translate(0, 0);
}

.modal-backdrop.fade {
  opacity: 0;
  transition: opacity 0.15s linear;
}

.modal-backdrop.show {
  opacity: 0.5;
}

</style>

<script>
import { capitalizeAll, phoneMask } from "@/helpers/utils.js";
import setNavPills from "@/assets/js/nav-pills.js";
import setTooltip from "@/assets/js/tooltip.js";
// import ProfileCard from "./components/ProfileCard.vue";
import MaterialInput from "@/components/MaterialInput.vue";
import MaterialButton from "@/components/MaterialButton.vue";
import PatientAccordion from "@/components/PatientAccordion.vue";
import PatientPersonalInfo from "@/components/PatientPersonalInfo.vue";
import PatientResponsibleInfo from "@/components/PatientResponsibleInfo.vue";
import PatientContactInfo from "@/components/PatientContactInfo.vue";
import PatientAddressInfo from "@/components/PatientAddressInfo.vue";
import BackButton from "@/components/BackButton.vue";
import Planejamento from "@/views/Planejamento.vue";
import Tratamento from "@/views/Tratamento.vue";
import { getEnderecoByCep } from "@/services/commonService";
import { uploadImage } from "@/services/imagensService";
import { getClinicaDentistas } from "@/services/dentistasService";
import {
  getPaciente,
  updatePaciente,
  adicionarMeioContato,
  excluirMeioContato,
  getFichaInicial,
  excluirPaciente,
} from "@/services/pacientesService";
import { getClinicas } from "@/services/clinicasService";
import cSwal from "@/utils/cSwal.js";

var isEditing = [];

var paciente = {
  clinica: {
    id: null,
  },
};
var originalPaciente = {};

var showTratamento = false;

var activeProfileTab = "perfilPessoal";

var activeTab = "perfil";

let clinicaSlug
let pacienteIdFicha

export default {
  name: "profile",
  components: {
    // ProfileCard,
    MaterialInput,
    MaterialButton,
    Planejamento,
    Tratamento,
    PatientAccordion,
    PatientPersonalInfo,
    PatientResponsibleInfo,
    PatientContactInfo,
    PatientAddressInfo,
    BackButton,
  },
  data() {
    return {
      clinicas: [],
      dataRespostaFicha: null,
      questoesFichaInicial: [],
      isLoading: {
        paciente: true,
        profilePicture: true,
      },
      dentistas: [],
      isEditing,
      novoContato: {
        tipo: "whatsapp",
        contato: "",
        descricao: "",
      },
      novoContatoMask: "",
      showMenu: false,
      paciente,
      originalPaciente,
      showTratamento,
      activeTab,
      activeProfileTab,
      // New states for edit mode of blocks
      isEditingPessoal: false,
      isEditingResponsavel: false,
      isEditingEndereco: false,
      // New states to track if blocks are filled (readonly)
      isFilledPessoal: false,
      isFilledResponsavel: false,
      isFilledEndereco: false,
      // State to track if any accordion is in edit mode
      isEditMode: false,
      // State to track if edit mode is active (for hiding the back button)
      editModeActive: false,
    };
  },
  computed: {
    isAnyEditing() {
      return this.isEditingPessoal ||
             this.isEditingResponsavel ||
             this.isEditingEndereco ||
             (this.isEditing && this.isEditing.meiosContatos);
    },
    getContatoPlaceholder() {
      var placeholder = null;
      switch (this.novoContato.tipo) {
        case "whatsapp":
          placeholder = "WhatsApp";
          break;
        case "celular":
          placeholder = "Celular";
          break;
        case "telefone":
          placeholder = "Telefone";
          break;
        case "email":
          placeholder = "E-mail";
          break;
      }

      return placeholder;
    },
    possuiWhatsapp() {
      return (
        this.paciente &&
        this.paciente.contatos &&
        this.paciente.contatos.some((contato) => contato.tipo === "whatsapp")
      );
    },
    whatsappNumero() {
      if (this.possuiWhatsapp) {
        const whatsappContato = this.paciente.contatos.find(
          (contato) => contato.tipo === "whatsapp"
        );
        return whatsappContato.contato;
      } else {
        return null;
      }
    },
    detalhesPessoais() {
      return this.paciente.detalhes_paciente
        ? this.paciente.detalhes_paciente.filter((detalhe) => detalhe.tipo == "pessoal")
        : [];
    },
    hasPendingChanges() {
      if (!this.originalPaciente || !this.paciente) return false;

      // Função para comparar objetos de forma mais confiável
      const compareObjects = (obj1, obj2) => {
        // Comparar propriedades simples
        const simpleProps = ['nome', 'cpf', 'rg', 'data_nascimento', 'como_conheceu',
                            'nome_mae', 'nome_pai', 'observacoes', 'dentista_id',
                            'responsavel_nome', 'responsavel_cpf', 'responsavel_rg',
                            'endereco_cep', 'endereco_estado', 'endereco_cidade',
                            'endereco_logradouro', 'endereco_numero', 'endereco_complemento'];

        for (const prop of simpleProps) {
          if (obj1[prop] !== obj2[prop]) {
            return true; // Encontrou uma diferença
          }
        }

        // Comparar clinica.id se existir
        if (obj1.clinica && obj2.clinica && obj1.clinica.id !== obj2.clinica.id) {
          return true;
        }

        return false; // Nenhuma diferença encontrada
      };

      return compareObjects(this.paciente, this.originalPaciente);
    },
  },
  watch: {
    paciente: {
      handler() {
        for (const propriedade in this.paciente)
          if (this.paciente[propriedade] === "") this.paciente[propriedade] = null;
      },
      deep: true, // Observação profunda de alterações aninhadas
    },
  },
  methods: {
    async refreshDentistas() {
      if (this.$clinica)
        this.dentistas = await getClinicaDentistas(this.$clinica.id);
    },
    async refreshClinicas() {
      if (!this.$user.system_admin)
        return
      this.clinicas = await getClinicas();
    },
    capitalizeAll,
    phoneMask,
    iniciarDiagnosticoPlanejamento() {
      this.openTab("planejamento");
    },

    updateConsultas(consultas) {
      if (consultas && Array.isArray(consultas)) {
        this.paciente.consultas = consultas;
      }
    },

    editarConsulta(id) {
      // Acessar o componente ConsultaModal através da referência e chamar o método
      this.$refs.consultaModal.abrirModalEditarConsulta(id);
    },

    verHistoricoConsulta(id) {
      // Acessar o componente HistoricoConsultaModal através da referência e chamar o método
      this.$refs.historicoModal.abrirModal(id);
    },

    recarregarConsultas() {
      // Recarregar o paciente inteiro para atualizar as consultas
      this.refreshPaciente();
    },
    profilePictureLoaded() {
      this.isLoading.profilePicture = false;
      this.isLoading.paciente = false; // Also hide the full screen spinner when profile picture is loaded
    },
    toggleFormularioView() {
      // Placeholder for toggling the form view modal if needed
    },

    getFichaInicialLink() {
      return `${window.location.origin}/bem-vindo/?t=${this.paciente.public_token}`;
    },

    contatoChange(event) {
      // Update the parent's novoContato object to stay in sync with the child component
      if (event && event.target) {
        this.novoContato.contato = event.target.value;
      }
    },

    cancelPhotoUpload() {
      this.pendingPhotoFile = null;
      this.photoPreviewImage = null;
    },

    confirmChangeName() {
      cSwal.cConfirm("Deseja alterar o nome do paciente?", () => {
        this.changeName();
      });
    },

    changeName() {
      cSwal.fire({
        title: 'Alterar nome do paciente',
        input: 'text',
        inputValue: this.paciente.nome,
        inputLabel: 'Novo nome:',
        inputPlaceholder: 'Digite o novo nome',
        showCancelButton: true,
        confirmButtonText: 'Salvar',
        cancelButtonText: 'Cancelar',
        customClass: {
          input: 'text-center',
          popup: 'swal-wider-popup'
        },
        inputValidator: (value) => {
          if (!value || value.trim() === '') {
            return 'O nome não pode estar vazio';
          }
        },
        didOpen: () => {
          // Adicionar estilo para centralizar o texto do input
          const style = document.createElement('style');
          style.innerHTML = `
            .swal2-input {
              text-align: center !important;
              font-size: 1.1em !important;
            }
            .swal-wider-popup {
              width: 32em !important;
            }
          `;
          document.head.appendChild(style);
        }
      }).then(async (result) => {
        if (result.isConfirmed) {
          const newName = result.value.trim();
          if (newName !== this.paciente.nome) {
            cSwal.loading("Atualizando nome do paciente...");

            // Aplicar capitalização ao nome (primeira letra de cada palavra em maiúscula)
            const capitalizedName = this.capitalizeAllStr(newName);

            // Criar uma cópia do paciente para atualização
            const pacienteToUpdate = { ...this.paciente };
            pacienteToUpdate.nome = capitalizedName;

            const update = await updatePaciente(pacienteToUpdate);

            if (update) {
              // Atualizar o nome localmente com a versão capitalizada
              this.paciente.nome = capitalizedName;
              this.originalPaciente.nome = capitalizedName;

              cSwal.loaded();
              cSwal.cSuccess("Nome do paciente atualizado com sucesso.");
            } else {
              cSwal.loaded();
              cSwal.cError("Ocorreu um erro ao atualizar o nome do paciente.");
            }
          }
        }
      });
    },

    confirmUpdatePhoto() {
      cSwal.cConfirm("Deseja atualizar a foto de perfil?", () => {
        this.chooseProfilePictureFile();
      });
    },

    chooseProfilePictureFile() {
      document.getElementById("profilePictureFileInput").value = "";
      document.getElementById("profilePictureFileInput").click();
    },

    profilePicUpload(e) {
      cSwal.loading("Atualizando imagem de perfil...");

      const imagem = e.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(imagem);
      reader.onload = async () => {
        const imgData = {
          paciente_id: this.paciente.id,
          imagem,
          dir: "profile_pic",
        };
        const upload = await uploadImage(imgData);

        if (upload) {
          await this.refreshPaciente();
          cSwal.loaded();
          cSwal.cSuccess("A foto de perfil do paciente foi atualizada.");
        } else {
          cSwal.loaded();
          cSwal.cError("Ocorreu um erro ao atualizar a foto de perfil do paciente.");
        }
      };
    },

    capitalizeAllStr(str) {
      return str.replace(/\b\w/g, (l) => l.toUpperCase());
    },

    normalizeEmptyStrings(obj) {
      // Função para normalizar strings vazias para null em um objeto
      if (obj === null || obj === undefined) return obj;

      if (typeof obj === 'string' && obj === '') {
        return null;
      }

      if (Array.isArray(obj)) {
        return obj.map(item => this.normalizeEmptyStrings(item));
      }

      if (typeof obj === 'object') {
        const normalized = {};
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            normalized[key] = this.normalizeEmptyStrings(obj[key]);
          }
        }
        return normalized;
      }

      return obj;
    },

    toggleEditMode(section) {
      this.isEditing[section] = !this.isEditing[section];
      this.isEditMode = this.isEditing[section];
    },

    handleEditModeChange(isEditing) {
      console.log('handleEditModeChange chamado com:', isEditing);
      this.isEditMode = isEditing;

      // Forçar a atualização da propriedade computada isAnyEditing
      if (isEditing) {
        // Se estamos entrando no modo de edição, verificamos qual accordion está aberto
        // e atualizamos o estado correspondente
        const accordions = document.querySelectorAll('.accordion-collapse.show');
        accordions.forEach(accordion => {
          const id = accordion.id;
          if (id === 'collapsePersonalInfo') {
            this.isEditingPessoal = true;
          } else if (id === 'collapseResponsibleInfo') {
            this.isEditingResponsavel = true;
          } else if (id === 'collapseContactInfo') {
            this.isEditing.meiosContatos = true;
          } else if (id === 'collapseAddressInfo') {
            this.isEditingEndereco = true;
          }
        });
      }

      console.log('isEditMode agora é:', this.isEditMode);
      console.log('isAnyEditing agora é:', this.isAnyEditing);
    },

    // Método para lidar com o evento edit-mode-active do componente Tratamento
    handleEditModeActive(isActive) {
      console.log('handleEditModeActive chamado com:', isActive);
      this.editModeActive = isActive;

      // A classe edit-mode será aplicada ao BackButton via binding :class
      // O componente BackButton vai verificar essa classe e se ocultar quando necessário
    },

    // New toggle methods for each block
    toggleEditPessoal() {
      this.isEditingPessoal = !this.isEditingPessoal;
      this.isEditMode = this.isEditingPessoal;
    },
    toggleEditResponsavel() {
      this.isEditingResponsavel = !this.isEditingResponsavel;
      this.isEditMode = this.isEditingResponsavel;
    },
    toggleEditEndereco() {
      this.isEditingEndereco = !this.isEditingEndereco;
      this.isEditMode = this.isEditingEndereco;
    },

    // New methods to check if blocks are mostly filled
    checkFilledPessoal() {
      // Consider fields in "Informações pessoais"
      const fields = [
        this.paciente.dentista_id,
        this.paciente.clinica && this.paciente.clinica.id,
        this.paciente.data_nascimento,
        this.paciente.como_conheceu,
        this.paciente.rg,
        this.paciente.nome_mae,
        this.paciente.cpf,
        this.paciente.nome_pai,
      ];
      return this.isMostlyFilled(fields);
    },
    checkFilledResponsavel() {
      // Consider fields in "Informações do responsável"
      const fields = [
        this.paciente.responsavel_nome,
        this.paciente.responsavel_rg,
        this.paciente.responsavel_cpf,
      ];
      return this.isMostlyFilled(fields);
    },
    checkFilledEndereco() {
      // Consider fields in "Endereço"
      const fields = [
        this.paciente.endereco_cep,
        this.paciente.endereco_logradouro,
        this.paciente.endereco_numero,
        this.paciente.endereco_complemento,
        this.paciente.endereco_cidade,
        this.paciente.endereco_estado,
      ];
      return this.isMostlyFilled(fields);
    },

    // Helper method to determine if majority of fields are filled
    isMostlyFilled(fields) {
      const total = fields.length;
      const filledCount = fields.filter((f) => f !== null && f !== undefined && f !== "").length;
      // Consider majority filled if more than half fields are filled
      return filledCount >= Math.ceil(total / 2);
    },
    clearNovoContato() {
      this.novoContato.contato = "";
      this.novoContato.descricao = "";
    },
    getContatoHref(tipo, contato) {
      switch (tipo) {
        case "email":
          return `mailto:${contato}`;
        case "whatsapp":
          return `https://wa.me/55${contato.replace(/\D+/g, "")}`;
        case "telefone":
        case "celular":
          return `tel:${contato.replace(/\D+/g, "")}`;
        default:
          return "#";
      }
    },

    excluirContato(id, tipo) {
      if (tipo == "whatsapp") tipo = "WhatsApp";
      else if (tipo == "email") tipo = "e-mail";

      cSwal.cConfirm("Deseja realmente excluir este " + tipo + "?", async () => {
        cSwal.loading("Excluindo contato...");
        const del = await excluirMeioContato(id);
        if (del) {
          await this.refreshPaciente({ onlyContatos: true });
          cSwal.loaded();
        } else {
          cSwal.loaded();
          cSwal.cError("Ocorreu um erro ao excluir o meio de contato");
        }
      });
    },

    async adicionarContato() {
      cSwal.loading("Adicionando contato...");
      const add = await adicionarMeioContato(this.paciente.id, this.novoContato);

      if (add) {
        await this.refreshPaciente({ onlyContatos: true });
        cSwal.loaded();
        this.clearNovoContato();
      } else {
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao salvar o contato.");
      }
    },
    selectMeioContato(tipo) {
      // Update the parent's novoContato object to stay in sync with the child component
      this.novoContato.tipo = tipo;
      // The focus is now handled in the child component
    },
    confirmSavePaciente() {
      cSwal.cConfirm("Deseja realmente salvar as alterações?", async () => {
        const update = await updatePaciente(this.paciente);

        if (update) {
          cSwal.cSuccess("As alterações foram salvas.");
          // Atualizar o originalPaciente para refletir as mudanças salvas
          this.originalPaciente = JSON.parse(JSON.stringify(this.paciente));
          // Não precisamos fazer o refreshPaciente completo, apenas atualizar o originalPaciente
          // await this.refreshPaciente();
        } else {
          cSwal.cError("Ocorreu um erro ao salvar as alterações.");
        }
      });
    },
    async handleFormLinkBtn() {
      if (this.possuiWhatsapp) this.enviarFormulario();
      else await this.copiarLink();
    },

    confirmarExcluirPaciente() {
      cSwal.cConfirm(
        "Tem certeza que deseja excluir este paciente? Esta ação não pode ser desfeita.",
        () => this.excluirPaciente(),
        {
          title: "Excluir paciente",
          icon: "warning",
          confirmButtonText: "Sim, excluir",
          cancelButtonText: "Cancelar",
        }
      );
    },

    async excluirPaciente() {
      cSwal.loading("Excluindo paciente...");

      try {
        const resultado = await excluirPaciente(this.paciente.id);

        if (resultado) {
          cSwal.loaded();
          cSwal.cSuccess("Paciente excluído com sucesso.");
          // Redirecionar para a lista de pacientes
          this.$router.push({ name: "Pacientes" });
        } else {
          cSwal.loaded();
          cSwal.cError("Não foi possível excluir o paciente. Tente novamente.");
        }
      } catch (error) {
        console.error("Erro ao excluir paciente:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir o paciente.");
      }
    },
    async copiarLink() {
      const link = this.getFichaInicialLink();

      if (!navigator.clipboard) {
        cSwal.cInfo(
          "Link da ficha de avaliação inicial para o paciente<br><b>" +
            this.paciente.nome +
            "</b>:<br><br><b>" +
            link +
            "</b>"
        );
        return false;
      }

      // Copy link to clipboard
      await navigator.clipboard
        .writeText(link)
        .then(() => {
          console.log("Link copied to clipboard!");
        })
        .catch((error) => {
          console.error("Error copying link:", error);
        });

      cSwal.cAlert("O link do formulário foi copiado.");
    },
    enviarFormulario() {
      const whatsappNumber = this.whatsappNumero;
      const phoneNumber = whatsappNumber.replace(/\D+/g, ""); // extract only numbers
      if (phoneNumber.length !== 11) {
        // show error message
        cSwal.cAlert("Número de WhatsApp inválido. Por favor, verifique o número.");
        return;
      }
      const link = this.getFichaInicialLink();
      const whatsappLink = `https://wa.me/55${phoneNumber}?text=Olá, bem-vindo a clínica! Por favor, preencha nosso formulário para lhe melhor atendermos: ${link}`;
      window.open(whatsappLink, "_blank"); // open in new tab
    },

    validarCep(cep) {
      if (!cep) return false;

      return /^\d{8}$/.test(cep.replace(/[^\d]+/g, ""));
    },

    async getEndereco(event) {
      clearTimeout(this.timeout);
      this.timeout = setTimeout(async () => {
        var cep = event.target.value;
        cep = this.paciente.endereco_cep;

        if (!this.validarCep(cep)) return false;

        const enderecoInfo = await getEnderecoByCep(cep);
        if (!enderecoInfo) return false;

        this.paciente.endereco_logradouro = enderecoInfo.street;
        this.paciente.endereco_cidade = enderecoInfo.city;
        this.paciente.endereco_estado = enderecoInfo.state;

        if (!this.paciente.endereco_numero) this.$refs.endereco_numero.getInput().focus();
      }, 50);
    },

    updatePacienteField({ field, value }) {
      // Handle special case for novoContato fields
      if (field.startsWith('novoContato.')) {
        const property = field.split('.')[1];
        this.novoContato[property] = value;
        return;
      }

      // Use lodash's set or a similar approach to handle nested paths
      if (field.includes('.')) {
        const parts = field.split('.');
        let obj = this.paciente;
        for (let i = 0; i < parts.length - 1; i++) {
          if (!obj[parts[i]]) {
            obj[parts[i]] = {};
          }
          obj = obj[parts[i]];
        }
        obj[parts[parts.length - 1]] = value;
      } else {
        this.paciente[field] = value;
      }

      // Forçar a detecção de mudanças criando um novo objeto paciente
      // Isso é necessário porque o Vue pode não detectar mudanças em objetos aninhados
      this.paciente = { ...this.paciente };
    },

    getContatoIcon(type) {
      var icon = null;
      switch (type) {
        case "whatsapp":
          icon = ["fab", "whatsapp"];
          break;
        case "celular":
          icon = ["fas", "mobile-screen-button"];
          break;
        case "telefone":
          icon = "mdi-phone";
          break;
        case "email":
          icon = ["fas", "envelope"];
          break;
      }
      return icon;
    },

    getInfoIcon(nivel) {
      var icon = null;
      switch (nivel) {
        case "positivo":
          icon = "thumbs-up";
          break;
        case "neutro":
          icon = "info-circle";
          break;
        case "atencao":
          icon = "circle-exclamation";
          break;
        case "negativo":
          icon = "thumbs-down";
          break;
      }

      return icon;
    },

    setProfileTab(tab) {
      this.activeProfileTab = tab;
    },

    openTab(tab) {
      this.activeTab = tab;
      setTimeout(() => {
        window.dispatchEvent(new Event("resize"));
      }, 100);
    },
    async refreshPaciente(options) {
      this.isEditing.meiosContatos = false;
      await this.getPacienteDetails(this.paciente.id_ficha, clinicaSlug, options);
    },
    async getPacienteDetails(idFicha, clinicaSlug, options) {
      this.isLoading.paciente = true;

      options = {
        onlyContatos: false,
        ...options,
      };
      const paciente = await getPaciente(idFicha, clinicaSlug);
      if (paciente && !options.onlyContatos) {
        // Normalizar strings vazias para null antes de definir os objetos
        const normalizedPaciente = this.normalizeEmptyStrings(JSON.parse(JSON.stringify(paciente)));
        this.paciente = normalizedPaciente;
        this.originalPaciente = JSON.parse(JSON.stringify(normalizedPaciente));

        // After loading paciente, check if blocks are filled
        this.isFilledPessoal = this.checkFilledPessoal();
        this.isFilledResponsavel = this.checkFilledResponsavel();
        this.isFilledEndereco = this.checkFilledEndereco();

        // Set edit mode based on filled status: if filled, readonly (edit mode off)
        this.isEditingPessoal = !this.isFilledPessoal;
        this.isEditingResponsavel = !this.isFilledResponsavel;
        this.isEditingEndereco = !this.isFilledEndereco;
      }

      else if (paciente && options.onlyContatos) {
        this.paciente.contatos = paciente.contatos;
        this.originalPaciente = {
          ...this.originalPaciente,
          contatos: paciente.contatos,
        };
      }

      else if (idFicha) {
          cSwal.cError("Ocorreu um erro ao tentar carregar os dados do paciente.");
      }

      this.isLoading.paciente = false;

      const fichaInicial = await getFichaInicial(this.paciente.id);
      if (fichaInicial) {
        this.questoesFichaInicial = fichaInicial.questoes;
        this.dataRespostaFicha = fichaInicial.data_resposta;
      }
    },
  },

  async created() {
    clinicaSlug = this.$route.params.clinica_slug || null;
    pacienteIdFicha = this.$route.params.id_ficha;
    this.getPacienteDetails(pacienteIdFicha, clinicaSlug);
  },

  async mounted() {
    this.refreshClinicas()
    setNavPills();
    setTooltip();
    this.$store.state.isAbsolute = true;

    this.refreshDentistas();
  },

  beforeUnmount() {}
};
</script>
