<?php

namespace App\Services;

use App\Models\ActionHistory;
use App\Models\User;
use App\Models\Paciente;
use App\Models\Dentista;
use App\Models\Clinica;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;

class ActionHistoryService
{
    /**
     * Log a create action.
     */
    public function logCreate(
        int $userId,
        Model $entity,
        array $newData,
        ?Request $request = null,
        ?string $description = null
    ): ?ActionHistory {
        try {
            $entityRelations = $this->extractEntityRelations($entity);

            return ActionHistory::create([
                'user_id' => $userId,
                'action_type' => ActionHistory::ACTION_CREATE,
                'action_description' => $description ?? $this->generateCreateDescription($entity),
                'http_method' => $request?->method(),
                'endpoint' => $request?->path(),
                'paciente_id' => $entityRelations['paciente_id'],
                'dentista_id' => $entityRelations['dentista_id'],
                'clinica_id' => $entityRelations['clinica_id'],
                'entity_type' => get_class($entity),
                'entity_id' => $entity->id,
                'old_data' => null,
                'new_data' => $this->sanitizeData($newData),
                'ip_address' => $request?->ip(),
                'user_agent' => $request?->userAgent(),
            ]);
        } catch (Exception $e) {
            Log::error('Failed to log create action: ' . $e->getMessage(), [
                'entity_type' => get_class($entity),
                'entity_id' => $entity->id,
                'user_id' => $userId,
            ]);
            return null;
        }
    }

    /**
     * Log an update action.
     */
    public function logUpdate(
        int $userId,
        Model $entity,
        array $oldData,
        array $newData,
        ?Request $request = null,
        ?string $description = null
    ): ?ActionHistory {
        try {
            $entityRelations = $this->extractEntityRelations($entity);

            return ActionHistory::create([
                'user_id' => $userId,
                'action_type' => ActionHistory::ACTION_UPDATE,
                'action_description' => $description ?? $this->generateUpdateDescription($entity, $oldData, $newData),
                'http_method' => $request?->method(),
                'endpoint' => $request?->path(),
                'paciente_id' => $entityRelations['paciente_id'],
                'dentista_id' => $entityRelations['dentista_id'],
                'clinica_id' => $entityRelations['clinica_id'],
                'entity_type' => get_class($entity),
                'entity_id' => $entity->id,
                'old_data' => $this->sanitizeData($oldData),
                'new_data' => $this->sanitizeData($newData),
                'ip_address' => $request?->ip(),
                'user_agent' => $request?->userAgent(),
            ]);
        } catch (Exception $e) {
            Log::error('Failed to log update action: ' . $e->getMessage(), [
                'entity_type' => get_class($entity),
                'entity_id' => $entity->id,
                'user_id' => $userId,
            ]);
            return null;
        }
    }

    /**
     * Log a delete action.
     */
    public function logDelete(
        int $userId,
        Model $entity,
        array $oldData,
        ?Request $request = null,
        ?string $description = null
    ): ?ActionHistory {
        try {
            $entityRelations = $this->extractEntityRelations($entity);

            return ActionHistory::create([
                'user_id' => $userId,
                'action_type' => ActionHistory::ACTION_DELETE,
                'action_description' => $description ?? $this->generateDeleteDescription($entity),
                'http_method' => $request?->method(),
                'endpoint' => $request?->path(),
                'paciente_id' => $entityRelations['paciente_id'],
                'dentista_id' => $entityRelations['dentista_id'],
                'clinica_id' => $entityRelations['clinica_id'],
                'entity_type' => get_class($entity),
                'entity_id' => $entity->id,
                'old_data' => $this->sanitizeData($oldData),
                'new_data' => null,
                'ip_address' => $request?->ip(),
                'user_agent' => $request?->userAgent(),
            ]);
        } catch (Exception $e) {
            Log::error('Failed to log delete action: ' . $e->getMessage(), [
                'entity_type' => get_class($entity),
                'entity_id' => $entity->id,
                'user_id' => $userId,
            ]);
            return null;
        }
    }

    /**
     * Extract entity relationships (paciente_id, dentista_id, clinica_id) from the entity.
     */
    private function extractEntityRelations(Model $entity): array
    {
        $relations = [
            'paciente_id' => null,
            'dentista_id' => null,
            'clinica_id' => null,
        ];

        // Direct entity types
        if ($entity instanceof Paciente) {
            $relations['paciente_id'] = $entity->id;
            $relations['dentista_id'] = $entity->dentista_id;
            $relations['clinica_id'] = $entity->clinica_id;
        } elseif ($entity instanceof Dentista) {
            $relations['dentista_id'] = $entity->id;
            $relations['clinica_id'] = $entity->clinica_id;
        } elseif ($entity instanceof Clinica) {
            $relations['clinica_id'] = $entity->id;
        } else {
            // For other entities, try to extract relationships from attributes
            if (isset($entity->paciente_id)) {
                $relations['paciente_id'] = $entity->paciente_id;
            }
            if (isset($entity->dentista_id)) {
                $relations['dentista_id'] = $entity->dentista_id;
            }
            if (isset($entity->clinica_id)) {
                $relations['clinica_id'] = $entity->clinica_id;
            }

            // Try to get relationships through loaded relations
            if ($entity->relationLoaded('paciente') && $entity->paciente) {
                $relations['paciente_id'] = $entity->paciente->id;
                $relations['dentista_id'] = $entity->paciente->dentista_id;
                $relations['clinica_id'] = $entity->paciente->clinica_id;
            }
            if ($entity->relationLoaded('dentista') && $entity->dentista) {
                $relations['dentista_id'] = $entity->dentista->id;
                $relations['clinica_id'] = $entity->dentista->clinica_id;
            }
            if ($entity->relationLoaded('clinica') && $entity->clinica) {
                $relations['clinica_id'] = $entity->clinica->id;
            }
        }

        return $relations;
    }

    /**
     * Sanitize data by removing sensitive information and limiting size.
     */
    private function sanitizeData(array $data): array
    {
        // Remove sensitive fields
        $sensitiveFields = ['password', 'password_confirmation', 'remember_token', 'api_token'];

        foreach ($sensitiveFields as $field) {
            unset($data[$field]);
        }

        // Limit the size of large text fields to prevent database issues
        foreach ($data as $key => $value) {
            if (is_string($value) && strlen($value) > 10000) {
                $data[$key] = substr($value, 0, 10000) . '... [truncated]';
            }
        }

        return $data;
    }

    /**
     * Generate a description for create actions.
     */
    private function generateCreateDescription(Model $entity): string
    {
        $entityName = class_basename($entity);
        $identifier = $this->getEntityIdentifier($entity);

        return "Created {$entityName}" . ($identifier ? " ({$identifier})" : '');
    }

    /**
     * Generate a description for update actions.
     */
    private function generateUpdateDescription(Model $entity, array $oldData, array $newData): string
    {
        $entityName = class_basename($entity);
        $identifier = $this->getEntityIdentifier($entity);
        $changedFields = array_keys(array_diff_assoc($newData, $oldData));

        $description = "Updated {$entityName}" . ($identifier ? " ({$identifier})" : '');

        if (!empty($changedFields)) {
            $description .= " - Changed: " . implode(', ', $changedFields);
        }

        return $description;
    }

    /**
     * Generate a description for delete actions.
     */
    private function generateDeleteDescription(Model $entity): string
    {
        $entityName = class_basename($entity);
        $identifier = $this->getEntityIdentifier($entity);

        return "Deleted {$entityName}" . ($identifier ? " ({$identifier})" : '');
    }

    /**
     * Get a human-readable identifier for the entity.
     */
    private function getEntityIdentifier(Model $entity): ?string
    {
        // Try common identifier fields
        $identifierFields = ['nome', 'name', 'title', 'email', 'id_ficha', 'id_matricula'];

        foreach ($identifierFields as $field) {
            if (isset($entity->$field)) {
                return $entity->$field;
            }
        }

        return $entity->id ? "ID: {$entity->id}" : null;
    }
}
