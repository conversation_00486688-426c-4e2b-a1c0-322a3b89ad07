<template>
  <div class="tratamento-content">
    <div class="row">
      <!-- Zona de exclusão para metas arrastadas -->
      <div
        class="meta-delete-zone"
        :class="{ 'active': isDraggingMeta, 'highlight': isOverDeleteZone }"
        ref="deleteZone"
        @dragover.prevent="onDragOverDeleteZone"
        @dragleave.prevent="onDragLeaveDeleteZone"
        @drop.prevent="onDropOnDeleteZone"
      >
        <div class="delete-zone-content">
          <i class="fas fa-trash"></i>
          <span>Solte aqui para excluir</span>
        </div>
      </div>

      <div class="col-12 col-md-6 h-100">
        <div class="box primary" ref="aparatologiaBox">
          <p class="custom-card-header">
            Aparatologia<span class="edit-icon-wrapper" :class="{ 'active': isEditing['aparatologia'] }" @click="isEditing['aparatologia'] ? cancelarEdicaoAparatologia('aparatologia') : toggleEditMode('aparatologia')">
              <font-awesome-icon
                :icon="['fas', 'edit']"
                class="edit-icon"
                :title="isEditing['aparatologia'] ? 'Sair do modo de edição' : 'Editar as informações da aparatologia'"
              />
            </span>
            <span
              v-if="isEditing.aparatologia"
              class="text-capitalize text-light pointer ms-2"
              @click="cancelarEdicaoAparatologia('aparatologia')"
              ><u>Cancelar edição</u></span
            >
          </p>

          <div
            v-if="isEditing['aparatologia']"
            class="d-flex flex-row w-100 justify-center my-3 d-md-flex d-none"
          >
            <button
              class="btn btn-sm btn-primary mb-0 btn-edit"
              title="Salvar as alterações realizadas"
              @click="salvarAparatologiaData"
            >
              Salvar
            </button>
          </div>

          <div class="card-body p-0 card-top-border">
            <table class="table-sm table-striped w-100 table-aparatologia">
              <tbody>
                <tr v-for="item in aparatologiaItems" v-bind:key="item.id">
                  <td>
                    <strong>{{ item.name }}:</strong>
                  </td>
                  <td>
                    <span v-if="!isEditing['aparatologia']">{{ item.text || 'Não definido' }}</span>
                    <select
                      v-if="isEditing['aparatologia'] && item.type == 'options'"
                      class="form-select select-sm"
                      v-model="item.text"
                      style="min-width: 170px"
                    >
                      <option value="">Selecione...</option>
                      <option
                        v-for="option in item.options"
                        v-bind:key="option.id"
                        :class="'text-' + option.mood"
                      >
                        {{ option.text }}
                      </option>
                      <option>Outro (especificar)...</option>
                    </select>
                    <input
                      v-if="isEditing['aparatologia'] && item.text === 'Outro (especificar)...'"
                      class="form-control input-sm mt-1"
                      v-model="item.customText"
                      placeholder="Especifique..."
                    />
                  </td>
                </tr>
                <tr>
                  <td>
                    <strong>Observações:</strong>
                  </td>
                  <td>
                    <span v-if="!isEditing['aparatologia']">{{ aparatologiaData.observacoes || 'Nenhuma observação' }}</span>
                    <textarea
                      v-if="isEditing['aparatologia']"
                      class="form-control form-control-sm mt-1"
                      v-model="aparatologiaData.observacoes"
                      rows="3"
                      placeholder="Observações adicionais"
                    ></textarea>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Botão FAB de Editar (visível quando não está em modo de edição) -->
          <transition name="fade">
            <button
              v-if="!isEditing['aparatologia'] && isMobile && isAparatologiaVisible"
              class="edit-fab d-md-none"
              @click="ativarEdicaoMobile()"
            >
              <font-awesome-icon :icon="['fas', 'edit']" class="me-2" />
              <span class="edit-fab-text">Editar</span>
            </button>
          </transition>

          <!-- Botões flutuantes para mobile (sm e abaixo) quando em modo de edição -->
          <transition name="fade">
            <div class="floating-buttons" v-if="isEditing['aparatologia'] && isMobile">
              <!-- Botões superiores (Salvar) -->
              <div class="floating-buttons-top">
                <button
                  class="save-fab"
                  :class="{'save-fab-disabled': !hasAparatologiaChanges}"
                  @click="salvarAparatologiaData"
                  :disabled="!hasAparatologiaChanges"
                >
                  <font-awesome-icon :icon="['fas', 'save']" class="me-2" />
                  <span>Salvar</span>
                </button>
              </div>

              <!-- Botão inferior (Cancelar) -->
              <div class="floating-buttons-bottom">
                <button
                  class="cancel-fab"
                  @click="cancelarEdicaoAparatologia()"
                >
                  <font-awesome-icon :icon="['fas', 'times']" class="me-2" />
                  <span>Cancelar</span>
                </button>
              </div>
            </div>
          </transition>
        </div>
      </div>

      <div class="col-12 col-md-6">
        <div class="box primary mt-4 mt-md-0" ref="contencaoBox">
          <p class="custom-card-header">
            Contenção<span class="edit-icon-wrapper" :class="{ 'active': isEditing['contencao'] }" @click="isEditing['contencao'] ? cancelarEdicaoAparatologia('contencao') : toggleEditMode('contencao')">
              <font-awesome-icon
                :icon="['fas', 'edit']"
                class="edit-icon"
                :title="isEditing['contencao'] ? 'Sair do modo de edição' : 'Editar as informações da contenção?'"
              />
            </span>
            <span
              v-if="isEditing.contencao"
              class="text-capitalize text-light pointer ms-2"
              @click="cancelarEdicaoAparatologia('contencao')"
              ><u>Cancelar edição</u></span
            >
          </p>

          <div
            v-if="isEditing['contencao']"
            class="d-flex flex-row w-100 justify-center my-3 d-md-flex d-none"
          >
            <button
              class="btn btn-sm btn-primary mb-0 btn-edit"
              title="Salvar as alterações realizadas"
              @click="salvarAparatologiaData"
            >
              Salvar
            </button>
          </div>

          <div class="card-body p-0 card-top-border">
            <table class="table-sm table-striped w-100 table-aparatologia">
              <tbody>
                <tr v-for="item in contencaoItems" v-bind:key="item.id">
                  <td>
                    <strong>{{ item.name }}:</strong>
                  </td>
                  <td>
                    <span v-if="!isEditing['contencao']">{{ item.text || 'Não definido' }}</span>
                    <select
                      v-if="isEditing['contencao'] && item.type == 'options'"
                      class="form-select select-sm"
                      v-model="item.text"
                      style="min-width: 170px"
                    >
                      <option value="">Selecione...</option>
                      <option
                        v-for="option in item.options"
                        v-bind:key="option.id"
                        :class="'text-' + option.mood"
                      >
                        {{ option.text }}
                      </option>
                      <option>Outro (especificar)...</option>
                    </select>
                    <input
                      v-if="isEditing['contencao'] && item.text === 'Outro (especificar)...'"
                      class="form-control input-sm mt-1"
                      v-model="item.customText"
                      placeholder="Especifique..."
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Botão FAB de Editar (visível quando não está em modo de edição) -->
          <transition name="fade">
            <button
              v-if="!isEditing['contencao'] && isMobile && isAparatologiaVisible"
              class="edit-fab d-md-none"
              @click="ativarEdicaoMobile()"
            >
              <font-awesome-icon :icon="['fas', 'edit']" class="me-2" />
              <span class="edit-fab-text">Editar</span>
            </button>
          </transition>

          <!-- Botões flutuantes para mobile (sm e abaixo) quando em modo de edição -->
          <transition name="fade">
            <div class="floating-buttons" v-if="isEditing['contencao'] && isMobile">
              <!-- Botões superiores (Salvar) -->
              <div class="floating-buttons-top">
                <button
                  class="save-fab"
                  :class="{'save-fab-disabled': !hasAparatologiaChanges}"
                  @click="salvarAparatologiaData"
                  :disabled="!hasAparatologiaChanges"
                >
                  <font-awesome-icon :icon="['fas', 'save']" class="me-2" />
                  <span>Salvar</span>
                </button>
              </div>

              <!-- Botão inferior (Cancelar) -->
              <div class="floating-buttons-bottom">
                <button
                  class="cancel-fab"
                  @click="cancelarEdicaoAparatologia()"
                >
                  <font-awesome-icon :icon="['fas', 'times']" class="me-2" />
                  <span>Cancelar</span>
                </button>
              </div>
            </div>
          </transition>
        </div>
      </div>
      <div class="col-12">
        <div class="box primary mt-4" ref="metasTerapeuticasBox">
          <p class="custom-card-header">
            Metas terapêuticas
            <span class="add-icon-wrapper" title="Adicionar meta terapêutica" data-bs-toggle="modal" data-bs-target="#modalMetaTerapeutica">
              <font-awesome-icon
                :icon="['fas', 'plus']"
                class="add-icon"
              />
            </span>
          </p>

          <!-- Quadrantes de metas terapêuticas -->
          <div class="metas-quadrantes-container">
            <!-- Layout para desktop (2x2) -->
            <div class="row g-3 d-none d-md-flex">
              <!-- Primeira linha -->
              <div class="col-md-6">
                <div class="quadrante-metas pendentes">
                  <div class="quadrante-header">
                    <h5>PENDENTES ({{ metasPendentes.length }})</h5>
                    <p class="quadrante-info">Metas que ainda precisam ser trabalhadas</p>
                  </div>
                  <draggable
                    class="quadrante-body row"
                    v-model="metasPendentes"
                    group="metas"
                    item-key="id"
                    @end="onDragEnd"
                    @start="onDragStart"
                    :animation="200"
                  >
                    <template #item="{element}">
                      <div class="col-md-6">
                        <div class="meta-card" :data-id="element.id">
                          <div class="meta-content">
                            <strong>{{ element.descricao }}</strong>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template #footer>
                      <div v-if="metasPendentes.length === 0" class="col-12">
                        <div class="empty-message">Não há metas pendentes</div>
                      </div>
                    </template>
                  </draggable>
                </div>
              </div>
              <div class="col-md-6">
                <div class="quadrante-metas concluidas">
                  <div class="quadrante-header">
                    <h5>CONCLUÍDAS ({{ metasConcluidas.length }})</h5>
                    <p class="quadrante-info">Metas que foram alcançadas com sucesso</p>
                  </div>
                  <draggable
                    class="quadrante-body row"
                    v-model="metasConcluidas"
                    group="metas"
                    item-key="id"
                    @end="onDragEnd"
                    @start="onDragStart"
                    :animation="200"
                  >
                    <template #item="{element}">
                      <div class="col-md-6">
                        <div class="meta-card" :data-id="element.id">
                          <div class="meta-content">
                            <strong>{{ element.descricao }}</strong>

                          </div>
                        </div>
                      </div>
                    </template>
                    <template #footer>
                      <div v-if="metasConcluidas.length === 0" class="col-12">
                        <div class="empty-message">Não há metas concluídas</div>
                      </div>
                    </template>
                  </draggable>
                </div>
              </div>

              <!-- Segunda linha -->
              <div class="col-md-6">
                <div class="quadrante-metas outra-area">
                  <div class="quadrante-header">
                    <h5>DEPENDE DE OUTRA ÁREA ({{ metasOutraArea.length }})</h5>
                    <p class="quadrante-info">Metas que dependem de outros profissionais</p>
                  </div>
                  <draggable
                    class="quadrante-body row"
                    v-model="metasOutraArea"
                    group="metas"
                    item-key="id"
                    @end="onDragEnd"
                    @start="onDragStart"
                    :animation="200"
                  >
                    <template #item="{element}">
                      <div class="col-md-6">
                        <div class="meta-card" :data-id="element.id">
                          <div class="meta-content">
                            <strong>{{ element.descricao }}</strong>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template #footer>
                      <div v-if="metasOutraArea.length === 0" class="col-12">
                        <div class="empty-message">Não há metas dependentes de outra área</div>
                      </div>
                    </template>
                  </draggable>
                </div>
              </div>
              <div class="col-md-6">
                <div class="quadrante-metas nao-concluidas">
                  <div class="quadrante-header">
                    <h5>NÃO FOI POSSÍVEL CONCLUIR ({{ metasNaoConcluidas.length }})</h5>
                    <p class="quadrante-info">Metas que não puderam ser alcançadas</p>
                  </div>
                  <draggable
                    class="quadrante-body row"
                    v-model="metasNaoConcluidas"
                    group="metas"
                    item-key="id"
                    @end="onDragEnd"
                    @start="onDragStart"
                    :animation="200"
                  >
                    <template #item="{element}">
                      <div class="col-md-6">
                        <div class="meta-card" :data-id="element.id">
                          <div class="meta-content">
                            <strong>{{ element.descricao }}</strong>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template #footer>
                      <div v-if="metasNaoConcluidas.length === 0" class="col-12">
                        <div class="empty-message">Não há metas não concluídas</div>
                      </div>
                    </template>
                  </draggable>
                </div>
              </div>
            </div>

            <!-- Layout para mobile (1x4) -->
            <div class="d-md-none">
              <div class="quadrante-metas pendentes mb-3">
                <div class="quadrante-header">
                  <h5>PENDENTES ({{ metasPendentes.length }})</h5>
                  <p class="quadrante-info">Metas que ainda precisam ser trabalhadas</p>
                </div>
                <draggable
                  class="quadrante-body"
                  v-model="metasPendentes"
                  group="metas"
                  item-key="id"
                  @end="onDragEnd"
                  @start="onDragStart"
                  :animation="200"
                >
                  <template #item="{element}">
                    <div class="meta-card" :data-id="element.id">
                      <div class="meta-content">
                        <strong>{{ element.descricao }}</strong>
                      </div>
                    </div>
                  </template>
                  <template #footer>
                    <div v-if="metasPendentes.length === 0" class="empty-message">
                      Não há metas pendentes
                    </div>
                  </template>
                </draggable>
              </div>

              <div class="quadrante-metas concluidas mb-3">
                <div class="quadrante-header">
                  <h5>CONCLUÍDAS ({{ metasConcluidas.length }})</h5>
                  <p class="quadrante-info">Metas que foram alcançadas com sucesso</p>
                </div>
                <draggable
                  class="quadrante-body"
                  v-model="metasConcluidas"
                  group="metas"
                  item-key="id"
                  @end="onDragEnd"
                  @start="onDragStart"
                  :animation="200"
                >
                  <template #item="{element}">
                    <div class="meta-card" :data-id="element.id">
                      <div class="meta-content">
                        <strong>{{ element.descricao }}</strong>
                      </div>
                    </div>
                  </template>
                  <template #footer>
                    <div v-if="metasConcluidas.length === 0" class="empty-message">
                      Não há metas concluídas
                    </div>
                  </template>
                </draggable>
              </div>

              <div class="quadrante-metas outra-area mb-3">
                <div class="quadrante-header">
                  <h5>DEPENDE DE OUTRA ÁREA  ({{ metasOutraArea.length }})</h5>
                  <p class="quadrante-info">Metas que dependem de outros profissionais</p>
                </div>
                <draggable
                  class="quadrante-body"
                  v-model="metasOutraArea"
                  group="metas"
                  item-key="id"
                  @end="onDragEnd"
                  @start="onDragStart"
                  :animation="200"
                >
                  <template #item="{element}">
                    <div class="meta-card" :data-id="element.id">
                      <div class="meta-content">
                        <strong>{{ element.descricao }}</strong>
                      </div>
                    </div>
                  </template>
                  <template #footer>
                    <div v-if="metasOutraArea.length === 0" class="empty-message">
                      Não há metas dependentes de outra área
                    </div>
                  </template>
                </draggable>
              </div>

              <div class="quadrante-metas nao-concluidas mb-3">
                <div class="quadrante-header">
                  <h5>NÃO FOI POSSÍVEL CONCLUIR ({{ metasNaoConcluidas.length }})</h5>
                  <p class="quadrante-info">Metas que não puderam ser alcançadas</p>
                </div>
                <draggable
                  class="quadrante-body"
                  v-model="metasNaoConcluidas"
                  group="metas"
                  item-key="id"
                  @end="onDragEnd"
                  @start="onDragStart"
                  :animation="200"
                >
                  <template #item="{element}">
                    <div class="meta-card" :data-id="element.id">
                      <div class="meta-content">
                        <strong>{{ element.descricao }}</strong>
                      </div>
                    </div>
                  </template>
                  <template #footer>
                    <div v-if="metasNaoConcluidas.length === 0" class="empty-message">
                      Não há metas não concluídas
                    </div>
                  </template>
                </draggable>
              </div>

            </div>
          </div>
        </div>
      </div>

      <div class="col-12">
        <!-- <div class="box primary mt-4 pb-2">
                    <p class="custom-card-header mb-3">Planejamento<font-awesome-icon :icon="['fas', 'edit']"
                            class="ml-3 pointer" :class="{ 'active': isEditing['planoTratamento'] }"
                            :title="isEditing['planoTratamento'] ? 'Sair do modo de edição' : 'Editar as metas terapêuticas'"
                            @click="toggleEditMode('planoTratamento')" /></p>

                    <div v-if="isEditing['planoTratamento']" class="d-flex flex-row w-100 justify-center mt-0 mb-3">
                        <button class="btn btn-sm btn-primary mb-0 btn-edit"
                            title="Adicionar uma nova fase de tratamento">
                            Adicionar fase
                        </button>
                        <div class="p-vertical-divider"></div>
                        <button class="btn btn-sm btn-primary mb-0 btn-edit" title="Salvar as alterações realizadas"
                            @click="save('fasesTratamento')">
                            Salvar
                        </button>
                    </div>

                    <div v-for="(fase, index) in paciente.fases_tratamento" v-bind:key="fase.id">
                        <div class="card mx-3 my-2">

                            <div class="fase-header d-flex flex-row">
                                <i class="fas fa-trash ms-4 text-danger-dark pointer"
                                    v-if="isEditing['planoTratamento']" title="Excluir esta fase do plano de tratamento"
                                    style="font-size: 14pt;"></i>

                                <div class="col d-flex flex-column" style="padding-left: 30px;">

                                    <span :class="{ 'active': fase.id == paciente.fase_atual.id }">
                                        <strong>
                                            <span v-if="!isEditing['planoTratamento']">
                                                Fase {{ index + 1 }}
                                                <span v-if="fase.id == paciente.fase_atual.id">
                                                    (atual)</span>:
                                            </span>

                                            <span v-if="!isEditing['planoTratamento']">{{ fase.nome }}</span>

                                            <div v-if="isEditing['planoTratamento']" class="l-input-group mb-1">
                                                <span>Fase {{ index + 1 }}</span>
                                                <input type="text"
                                                    class="form-control inline-input text-center input-sm"
                                                    v-model="fase.nome" style="max-width: 250px;">
                                            </div>
                                            ({{ $filters.howMuchTime(fase.data_inicio, fase.data_fim, false) }})
                                        </strong>
                                    </span>

                                    <span v-if="!isEditing['planoTratamento']" class="text-sm"
                                        :class="{ 'font-weight-bold active': fase.id == paciente.fase_atual.id }"
                                        :style="new Date() >= new Date(fase.data_fim) ? { 'text-decoration': 'line-through' } : {}">
                                        {{ $filters.dateDDY(fase.data_inicio) }}
                                        a
                                        {{ $filters.dateDDY(fase.data_fim) }}
                                    </span>

                                    <div v-if="isEditing['planoTratamento']"
                                        class="d-flex flex-row flex-wrap l-input-group mt-2 w-100 pe-2"
                                        style="margin: 0 auto;">
                                        <span>De</span>
                                        <input type="date" class="form-control input-sm" v-model="fase.data_inicio"
                                            style="max-width: 165px;" />
                                        <span>a</span>
                                        <input type="date" class="form-control input-sm" v-model="fase.data_fim"
                                            style="max-width: 165px;" />
                                    </div>
                                </div>
                            </div>

                            <div class="card-body px-4 py-3">
                                <strong>Objetivo</strong>:
                                <span v-if="!isEditing['planoTratamento']">{{ fase.objetivo }}</span>
                                <textarea v-if="isEditing['planoTratamento']" class="form-control"
                                    v-model="fase.objetivo"></textarea>

                                <div class="p-horizontal-divider m2"></div>
                                <strong>Mecânica</strong>:
                                <span v-if="!isEditing['planoTratamento']">{{ fase.mecanica }}</span>
                                <textarea v-if="isEditing['planoTratamento']" class="form-control"
                                    v-model="fase.mecanica"></textarea>

                                <div class="p-horizontal-divider m2"></div>
                                <strong>Acompanhamento</strong>:
                                <span v-if="!isEditing['planoTratamento']">{{ fase.acompanhamento }}</span>
                                <textarea v-if="isEditing['planoTratamento']" class="form-control"
                                    v-model="fase.acompanhamento"></textarea>
                            </div>
                        </div>

                        <font-awesome-icon v-if="index < paciente.fases_tratamento.length - 1"
                            :icon="['fas', 'arrow-down']" />
                    </div>
                </div> -->

        <div class="custom-card primary mt-4" ref="necessidadesSection">
          <p class="custom-card-header">
            Necessidades de encaminhamentos<span class="edit-icon-wrapper d-none d-md-inline-flex" :class="{ 'active': isEditing['necessidadesEncaminhamento'] }" @click="toggleEditMode('necessidadesEncaminhamento')">
              <font-awesome-icon
                :icon="['fas', 'edit']"
                class="edit-icon"
                :title="isEditing['necessidadesEncaminhamento'] ? 'Sair do modo de edição' : 'Editar as necessidades de encaminhamento'"
              />
            </span>
            <span
              v-if="isEditing.necessidadesEncaminhamento"
              class="text-capitalize text-light pointer ms-2"
              @click="toggleEditMode('necessidadesEncaminhamento')"
              ><u>Cancelar edição</u></span
            >
          </p>

          <!-- Botão Adicionar (visível apenas em modo de edição) -->
          <div
            v-if="isEditing['necessidadesEncaminhamento']"
            class="d-flex flex-row w-100 justify-center my-3 d-md-flex d-none"
          >
            <button
              class="btn btn-sm btn-primary mb-0 btn-edit"
              title="Adicionar uma nova necessidade de encaminhamento"
              data-bs-toggle="modal"
              data-bs-target="#modalNecessidadeEncaminhamento"
            >
              Adicionar
            </button>
            <div class="p-vertical-divider"></div>
            <button
              class="btn btn-sm btn-primary mb-0 btn-edit"
              title="Salvar as alterações realizadas"
              @click="salvarNecessidadesEncaminhamento"
            >
              Salvar
            </button>
          </div>

          <!-- Cards de necessidades de encaminhamento -->
          <div class="card-body p-3">
            <div v-if="necessidadesEncaminhamentos.length === 0" class="text-center text-secondary py-3">
              Não há necessidade de encaminhamentos.
            </div>

            <div v-else class="row g-3">
              <div v-for="(necessidade, index) in necessidadesEncaminhamentos" :key="necessidade.id" class="col-md-6 col-lg-4">
                <div class="necessidade-card">
                  <div class="necessidade-card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                      <span v-if="!isEditing['necessidadesEncaminhamento']">{{ necessidade.especialista }}</span>
                      <select
                        v-else
                        class="form-select form-select-sm"
                        v-model="necessidade.especialista"
                      >
                        <option v-for="especialista in especialistas" :key="especialista" :value="especialista">
                          {{ especialista }}
                        </option>
                      </select>
                    </h6>
                    <div v-if="isEditing['necessidadesEncaminhamento']" class="actions">
                      <button
                        class="btn btn-sm btn-danger"
                        @click="excluirNecessidade(necessidade.id)"
                        title="Excluir necessidade"
                      >
                        <font-awesome-icon :icon="['fas', 'trash']" />
                      </button>
                    </div>
                  </div>

                  <div class="necessidade-card-body">
                    <div class="mb-2">
                      <strong>Necessidade:</strong>
                      <div v-if="!isEditing['necessidadesEncaminhamento']">{{ necessidade.necessidade }}</div>
                      <input
                        v-else
                        type="text"
                        class="form-control form-control-sm mt-1"
                        v-model="necessidade.necessidade"
                        placeholder="Descreva a necessidade"
                      />
                    </div>

                    <div class="mb-2">
                      <strong>Observações:</strong>
                      <div v-if="!isEditing['necessidadesEncaminhamento']">{{ necessidade.observacoes || 'Nenhuma observação' }}</div>
                      <textarea
                        v-else
                        class="form-control form-control-sm mt-1"
                        v-model="necessidade.observacoes"
                        rows="2"
                        placeholder="Observações adicionais"
                      ></textarea>
                    </div>

                    <div>
                      <strong v-if="isEditing['necessidadesEncaminhamento']">Status:</strong>
                      <div v-if="!isEditing['necessidadesEncaminhamento']" class="mt-2 text-center">
                        <span class="status-badge" :class="{
                          'status-concluida': necessidade.status === 'CONCLUIDA',
                          'status-nao-concluida': necessidade.status === 'NAO_CONCLUIDA',
                          'status-pendente': necessidade.status === 'PENDENTE'
                        }">
                          {{ formatarStatus(necessidade.status) }}
                        </span>
                      </div>
                      <select
                        v-else
                        class="form-select form-select-sm mt-1"
                        v-model="necessidade.status"
                      >
                        <option value="PENDENTE">Pendente</option>
                        <option value="CONCLUIDA">Concluída</option>
                        <option value="NAO_CONCLUIDA">Não concluída</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Botão FAB de Editar (visível quando não está em modo de edição) -->
          <transition name="fade">
            <button
              v-if="!isEditing['necessidadesEncaminhamento'] && isMobile && isNecessidadesVisible"
              class="edit-fab d-md-none"
              @click="toggleEditMode('necessidadesEncaminhamento')"
            >
              <font-awesome-icon :icon="['fas', 'edit']" class="me-2" />
              <span class="edit-fab-text">Editar</span>
            </button>
          </transition>

          <!-- Removido o botão FAB de Adicionar que aparecia quando não estava em modo de edição -->

          <!-- Botões flutuantes para mobile (sm e abaixo) quando em modo de edição -->
          <transition name="fade">
            <div class="floating-buttons" v-if="isEditing['necessidadesEncaminhamento'] && isMobile">
              <!-- Botões superiores (Adicionar e Salvar) -->
              <div class="floating-buttons-top">
                <button
                  class="add-fab-inline me-2"
                  data-bs-toggle="modal"
                  data-bs-target="#modalNecessidadeEncaminhamento"
                >
                  <font-awesome-icon :icon="['fas', 'plus']" class="me-2" />
                  <span>Adicionar</span>
                </button>
                <button
                  class="save-fab"
                  :class="{'save-fab-disabled': !hasNecessidadesChanges}"
                  @click="salvarNecessidadesEncaminhamento"
                  :disabled="!hasNecessidadesChanges"
                >
                  <font-awesome-icon :icon="['fas', 'save']" class="me-2" />
                  <span>Salvar</span>
                </button>
              </div>

              <!-- Botão inferior (Cancelar) -->
              <div class="floating-buttons-bottom">
                <button
                  class="cancel-fab"
                  @click="toggleEditMode('necessidadesEncaminhamento')"
                >
                  <font-awesome-icon :icon="['fas', 'times']" class="me-2" />
                  <span>Cancelar</span>
                </button>
              </div>
            </div>
          </transition>
        </div>
      </div>

      <div v-if="!paciente.data_inicio_tratamento" class="p-horizontal-divider"></div>

      <div v-if="!paciente.data_inicio_tratamento" class="row d-flex flex-row flex-wrap justify-content-center">
        <div class="col-sm-6 col-md-4 d-flex flex-column align-items-center justify-content-center">
          <div class="custom-card w-auto text-center">
            <div class="custom-card-header">
              <label class="form-label fw-bold mb-2 px-3" style="font-size: 1.1rem; color: #555;">
                DATA DO PLANEJAMENTO
              </label>
            </div>
            <input
              type="date"
              class="form-control text-center"
              v-model="dataPlanejamento"
              style="max-width: 250px;"
            />
            <div class="py-1" style="font-weight: 500; color: #555; border-top: 1px solid #CCC;">
              {{ $filters.howMuchTime(dataPlanejamento, { type: 'date' }) }}
            </div>
          </div>
        </div>

        <div class="col-sm-6 col-md-4 d-flex flex-column align-items-center justify-content-center">
          <div class="custom-card w-auto text-center">
            <div class="custom-card-header">
              <label class="form-label fw-bold mb-2 px-3" style="font-size: 1.1rem; color: #555;">
                INÍCIO DO TRATAMENTO
              </label>
            </div>
            <input
              type="date"
              class="form-control text-center"
              v-model="dataInicioTratamento"
              style="max-width: 250px;"
            />
            <div class="py-1" style="font-weight: 500; color: #555; border-top: 1px solid #CCC;">
              {{ $filters.howMuchTime(dataInicioTratamento, { type: 'date' }) }}
            </div>
          </div>
        </div>

        <div class="col-sm-6 col-md-4 mt-3 mt-md-0 d-flex flex-column align-items-center justify-content-center">
          <div class="custom-card w-auto text-center">
            <div class="custom-card-header">
              <label class="form-label fw-bold mb-2 px-3" style="font-size: 1.1rem; color: #555;">
                TÉRMINO PREVISTO (EM MESES)
              </label>
            </div>
            <input
              type="number"
              class="form-control text-center mx-auto"
              v-model.number="terminoPrevistoMeses"
              min="0"
              style="max-width: 100px;"
            />
            <div class="py-1" style="font-weight: 500; color: #555; border-top: 1px solid #CCC;">
              {{ dataPrevista ? $filters.dateDmy(dataPrevista) : '--/--/----' }}
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12" v-if="!paciente.data_inicio_tratamento">
        <div class="p-horizontal-divider"></div>

        <div class="next-btn-container py-2 py-md-3">
          <button
            class="btn btn-success mb-0"
            @click="confirmIniciarTratamento"
          >
            <i class="me-2 fas fa-play" style="font-size: 13pt"></i>
            <span style="font-size: 10pt"> INICIAR TRATAMENTO </span>
          </button>
        </div>
      </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalMetaTerapeutica" ref="modalMetaTerapeutica">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Adicionar meta terapêutica</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              ref="closeModalNovaMetaTerapeutica"
            ></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-12 d-flex flex-column text-center">
                <label for="metaTerapeutica">Meta terapêutica:</label>
                <MaterialInput
                  type="text"
                  class="my-2 text-center"
                  id="metaTerapeutica"
                  name="metaTerapeutica"
                  ref="metaTerapeutica"
                  v-model="novaMetaTerapeutica"
                />
                <button
                  class="btn btn-primary my-3 mx-auto"
                  @click="_adicionarMetaTerapeutica"
                  style="max-width: 200px"
                >
                  Adicionar
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal para adicionar necessidade de encaminhamento -->
    <div class="modal fade" tabindex="-1" id="modalNecessidadeEncaminhamento" ref="modalNecessidadeEncaminhamento">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Adicionar necessidade de encaminhamento</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              ref="closeModalNecessidadeEncaminhamento"
            ></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-12">
                <div class="mb-3">
                  <label for="especialista" class="form-label">Especialista:</label>
                  <select
                    class="form-select"
                    id="especialista"
                    v-model="novaNecessidade.especialista"
                    required
                  >
                    <option value="" disabled selected>Selecione um especialista</option>
                    <option v-for="especialista in especialistas" :key="especialista" :value="especialista">
                      {{ especialista }}
                    </option>
                  </select>
                </div>

                <div class="mb-3">
                  <label for="necessidade" class="form-label">Necessidade:</label>
                  <input
                    type="text"
                    class="form-control"
                    id="necessidade"
                    v-model="novaNecessidade.necessidade"
                    placeholder="Descreva a necessidade"
                    required
                  />
                </div>

                <div class="mb-3">
                  <label for="observacoes" class="form-label">Observações:</label>
                  <textarea
                    class="form-control"
                    id="observacoes"
                    v-model="novaNecessidade.observacoes"
                    rows="3"
                    placeholder="Observações adicionais"
                  ></textarea>
                </div>

                <div class="mb-3">
                  <label for="status" class="form-label">Status:</label>
                  <select
                    class="form-select"
                    id="status"
                    v-model="novaNecessidade.status"
                    required
                  >
                    <option value="PENDENTE">Pendente</option>
                    <option value="CONCLUIDA">Concluída</option>
                    <option value="NAO_CONCLUIDA">Não concluída</option>
                  </select>
                </div>

                <div class="text-center mt-4">
                  <button
                    class="btn btn-primary"
                    @click="adicionarNecessidade"
                    :disabled="!isNovaNecessidadeValida"
                  >
                    Adicionar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- <div class="modal fade" tabindex="-1" id="confirmIniciarTratamento">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Iniciar tratamento</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-12 d-flex flex-column text-center">
              <label for="dataInicioTratamento" class="text-md"
                >Insira a data de início do tratamento:</label
              >
              <input
                type="date"
                class="form-control text-center"
                id="dataInicioTratamento"
                v-model="dataInicioTratamento"
                style="font-size: 14pt"
              />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="closeInicioTratamentoModal">
            Cancelar
          </button>
          <button type="button" class="btn btn-primary" @click="_iniciarTratamento()">
            Confirmar
          </button>
        </div>
      </div>
    </div>
  </div> -->
</template>

<style scoped>
.tratamento-tab {
  text-align: center;
}

.fase-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: linear-gradient(to right, #e8e8e8, #ddd, #e8e8e8);
  border-radius: 10px 10px 0px 0px;
  padding: 7px 5px;
}

.table-aparatologia td {
  padding: 10px 10px;
  text-align: center;
  border-bottom: 1px solid #ddd;
}

.table-aparatologia tr > td:first-child {
  width: 40%;
  background: #f8f8f8;
  border-right: 1px solid #ddd;
}

.table-aparatologia tr > td:last-child {
  font-weight: 500;
  font-size: 11pt;
}

.card {
  border: 1px solid #ddd;
}

.card-body {
  font-size: 1rem;
  font-weight: 300;
}

/* Estilos para os quadrantes de metas terapêuticas */
.metas-quadrantes-container {
  margin: 1rem 0;
  padding: 0 1rem;
}

.quadrante-metas {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.quadrante-metas:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.quadrante-metas.pendentes {
  border: 1px solid #e0e0e0;
}

.quadrante-metas.pendentes .quadrante-header {
  background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
  color: #333;
}

.quadrante-metas.concluidas {
  border: 1px solid #c8e6c9;
}

.quadrante-metas.concluidas .quadrante-header {
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
  color: #2e7d32;
}

.quadrante-metas.nao-concluidas {
  border: 1px solid #ffcdd2;
}

.quadrante-metas.nao-concluidas .quadrante-header {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  color: #c62828;
}

.quadrante-metas.outra-area {
  border: 1px solid #ffe0b2;
}

.quadrante-metas.outra-area .quadrante-header {
  background: linear-gradient(135deg, #fff8e1, #ffe0b2);
  color: #ef6c00;
}

.quadrante-header p {
  padding-bottom: 0px;
}

.quadrante-header {
  padding: 8px 12px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.quadrante-header h5 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.quadrante-info {
  margin: 3px 0 0;
  font-size: 0.75rem;
  opacity: 0.8;
}

.quadrante-body {
  padding: 10px;
  background-color: #fff;
  flex-grow: 1;
  overflow-y: auto;
  max-height: 300px;
}

.quadrante-body .empty-message {
  text-align: center;
  padding: 15px 10px;
  color: #888;
  font-style: italic;
  font-size: 0.9rem;
}

.meta-card {
  background: #fff;
  border-radius: 6px;
  padding: 10px 12px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
  width: 100%;
}

.pendentes .meta-card {
  border-left: 3px solid #e0e0e0;
}

.concluidas .meta-card {
  border-left: 3px solid #c8e6c9;
}

.nao-concluidas .meta-card {
  border-left: 3px solid #ffcdd2;
}

.outra-area .meta-card {
  border-left: 3px solid #ffe0b2;
}

.meta-card:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.meta-card:active {
  cursor: grabbing;
}

.meta-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.meta-card:hover .meta-actions {
  opacity: 1;
}

/* Estilos para transição do modal */
.modal.fade {
  transition: opacity 0.3s ease;
}

.modal.fade .modal-dialog {
  transition: transform 0.3s ease;
  transform: translateY(-20px);
}

.modal.show .modal-dialog {
  transform: translateY(0);
}

.modal-backdrop.fade {
  transition: opacity 0.3s ease;
}

/* Estilos para a zona de exclusão */
.meta-delete-zone {
  position: fixed;
  bottom: -80px;
  left: 50%;
  transform: translateX(-50%);
  width: 220px;
  height: 70px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 2px dashed #dc3545;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Valor muito alto para garantir que fique acima de tudo */
  transition: all 0.3s ease;
  opacity: 0;
  box-shadow: 0 0 15px rgba(220, 53, 69, 0.2);
  pointer-events: none; /* Inicialmente não intercepta eventos */
}

.meta-delete-zone.active {
  bottom: 20px;
  opacity: 1;
  pointer-events: auto; /* Intercepta eventos quando ativo */
}

.meta-delete-zone.highlight {
  background-color: rgba(255, 235, 238, 0.98);
  border: 2px solid #dc3545;
  box-shadow: 0 0 20px rgba(220, 53, 69, 0.4);
  transform: translateX(-50%) scale(1.05);
}

/* Estilos para os elementos draggable */
.sortable-chosen {
  z-index: 1; /* Garante que o item arrastado fique abaixo da zona de exclusão */
}

.delete-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #dc3545;
}

.delete-zone-content i {
  font-size: 24px;
  margin-bottom: 5px;
}

.delete-zone-content span {
  font-size: 14px;
  font-weight: 500;
}

/* Estilos para os cards de necessidades de encaminhamento */
.necessidade-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 15px;
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #eaeaea;
}

.necessidade-card:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
  transform: translateY(-3px);
  border-color: #d0d0d0;
}

.necessidade-card-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 14px 16px;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
}

.necessidade-card-header h6 {
  font-weight: 600;
  color: #444;
  font-size: 1rem;
}

.necessidade-card-body {
  padding: 16px;
  flex-grow: 1;
  background-color: #fcfcfc;
}

.necessidade-card-body strong {
  color: #555;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 4px;
}

/* Estilos para o botão FAB de editar - apenas para mobile */
@media (max-width: 767.98px) {
  .edit-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    min-width: 120px;
    height: 50px;
    border-radius: 25px;
    background-color: #29618b;
    color: white;
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    z-index: 1000;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0 20px;
  }

  .edit-fab:hover {
    background-color: #1e4c6e;
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    /* Sem animação de pulsação conforme preferência do usuário */
  }
}

/* Estilos para o texto do botão FAB */
.edit-fab-text {
  margin-left: 5px;
}

/* Estilos para os botões flutuantes - apenas para mobile */
@media (max-width: 767.98px) {
  .floating-buttons {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 10px;
  }

  .floating-buttons-top {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    width: 100%;
  }

  .floating-buttons-bottom {
    display: flex;
    justify-content: center;
    width: 100%;
  }
}

/* Estilos para os botões de ação - apenas para mobile */
@media (max-width: 767.98px) {
  /* Estilo para o botão Adicionar inline */
  .add-fab-inline {
    min-width: 120px;
    height: 50px;
    border-radius: 25px;
    background-color: #4CAF50;
    color: white;
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0 20px;
  }

  .add-fab-inline:hover {
    background-color: #43A047;
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
  }

  /* Estilo para o botão Cancelar */
  .cancel-fab {
    min-width: 120px;
    height: 50px;
    border-radius: 25px;
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0 20px;
  }

  .cancel-fab:hover {
    background-color: #e9ecef;
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  /* Estilo para o botão Salvar */
  .save-fab {
    min-width: 120px;
    height: 50px;
    border-radius: 25px;
    background-color: #29618b;
    color: white;
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0 20px;
  }
}

@media (max-width: 767.98px) {
  .save-fab:hover:not(:disabled) {
    background-color: #1e4c6e;
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
  }

  .save-fab:disabled, .save-fab-disabled {
    background-color: #8badc2 !important;
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    box-shadow: none !important;
    pointer-events: none !important;
  }
}

/* Transições */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.fade-enter-to, .fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

/* Ocultar o botão Voltar quando o modo de edição está ativo */
:deep(.back-btn.edit-mode) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Ocultar elementos de edição desktop no mobile */
@media (max-width: 767.98px) {
  .custom-card-header .fa-edit,
  .custom-card-header .text-capitalize.text-light.pointer {
    display: none !important;
  }
}

/* Estilos para os badges de status */
.status-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.8rem;
  text-align: center;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  text-transform: uppercase;
  min-width: 100px;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.12);
}

.status-concluida {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 3px solid #4caf50;
}

.status-pendente {
  background-color: #fff8e1;
  color: #f57c00;
  border-left: 3px solid #ffc107;
}

.status-nao-concluida {
  background-color: #ffebee;
  color: #c62828;
  border-left: 3px solid #f44336;
}
</style>

<script>
import cSwal from "@/utils/cSwal.js";
import {
  adicionarMetaTerapeutica,
  excluirMetaTerapeutica,
  iniciarTratamento,
  updateMetaStatus,
  adicionarNecessidadeEncaminhamento,
  atualizarNecessidadeEncaminhamento,
  excluirNecessidadeEncaminhamento,
  salvarAparatologia,
  atualizarAparatologia
} from "@/services/tratamentosService";
import MaterialInput from "@/components/MaterialInput.vue";
import draggable from 'vuedraggable';

const aparatologia = [
  {
    id: 10,
    name: "Aparelho utilizado",
    text: "autoligado metálico bidimensional",
    observation: "",
    type: "options",
    options: [
      { id: 10, text: "autoligado metálico bidimensional" },
      { id: 20, text: "autoligado estético" },
      { id: 30, text: "alinhadores" },
      { id: 40, text: "miofuncional" },
      { id: 50, text: "autoligado + miofuncional" },
      { id: 60, text: "alinhadores + miofuncional" },
    ],
  },
  // {
  //     id: 20,
  //     name: 'Tipo de colagem',
  //     text: 'Alexander (x = centro da coroa clínica pré-molar)',
  //     observation: '',
  //     type: 'options',
  //     options: [
  //         { id: 70, text: 'agradávRoth (x = 4)el' },
  //         { id: 80, text: 'Alexander (x = centro da coroa clínica pré-molar)' },
  //         { id: 90, text: 'Pitts (smile arc)' },
  //         { id: 95, text: 'SADB' },
  //     ],
  // },
  // {
  //     id: 50,
  //     name: 'Necessidade de exercícios miofuncionais',
  //     text: 'nenhuma',
  //     observation: '',
  //     type: 'options',
  //     options: [
  //         { id: 150, text: 'nenhuma' },
  //         { id: 160, text: 'respiração' },
  //         { id: 170, text: 'deglutição' },
  //         { id: 180, text: 'posição de língua' },
  //     ]
  // },
];

const contencao = [
  {
    id: 30,
    name: "Contenção superior",
    text: "placa acetato",
    observation: "",
    type: "options",
    options: [
      { id: 100, text: "placa acetato" },
      { id: 105, text: "placa acetato com posicionado de língua" },
      { id: 110, text: "placa Hawley" },
      { id: 115, text: "placa picvem" },
      { id: 120, text: "placa acetato + placa de Michigan" },
      { id: 125, text: "contenção fixa 3x3" },
    ],
  },
  {
    id: 40,
    name: "Contenção inferior",
    text: "placa acetato",
    observation: "",
    type: "options",
    options: [
      { id: 130, text: "placa acetato" },
      { id: 140, text: "contenção fixa 3x3" },
    ],
  },
  // {
  //     id: 50,
  //     name: 'Necessidade de exercícios miofuncionais',
  //     text: 'nenhuma',
  //     observation: '',
  //     type: 'options',
  //     options: [
  //         { id: 150, text: 'nenhuma' },
  //         { id: 160, text: 'respiração' },
  //         { id: 170, text: 'deglutição' },
  //         { id: 180, text: 'posição de língua' },
  //     ]
  // },
];

// Lista de especialistas para o select
const especialistas = [
  "Ortodontista",
  "Implantodontista",
  "Endodontista",
  "Periodontista",
  "Protesista",
  "Odontopediatra",
  "Cirurgião Bucomaxilofacial",
  "Dentística",
  "Radiologista",
  "Estomatologista",
  "Harmonização Orofacial",
  "Clínico Geral",
  "Outro"
];

var isEditing = {
  necessidadesEncaminhamento: false,
  aparatologia: false,
  contencao: false
};

let dataInicioTratamento = new Date().toISOString().split("T")[0];

export default {
  name: "PlanoTratamento",
  props: {
    paciente: {
      type: Object,
    },
  },
  data() {
    return {
      novaMetaTerapeutica: "",
      isEditing,
      aparatologiaItems: JSON.parse(JSON.stringify(aparatologia)),
      contencaoItems: JSON.parse(JSON.stringify(contencao)),
      aparatologiaData: {
        id: null,
        paciente_id: null,
        aparelho_utilizado: "",
        contencao_superior: "",
        contencao_inferior: "",
        observacoes: ""
      },
      aparatologiaOriginal: null,
      isAparatologiaLoaded: false,
      isAparatologiaSectionInView: false,
      dataInicioTratamento,
      dataPlanejamento: new Date().toISOString().split("T")[0],
      terminoPrevistoMeses: 0,
      isDraggingMeta: false,
      isOverDeleteZone: false,
      wasDroppedOnDeleteZone: false,
      currentDraggedMetaId: null,
      especialistas,
      novaNecessidade: {
        especialista: "",
        necessidade: "",
        observacoes: "",
        status: "PENDENTE"
      },
      necessidadesOriginais: [],
      isNecessidadesSectionInView: false,
      editModeActive: false // Variável para controlar a visibilidade do botão Voltar
    };
  },
  // Computed properties will be defined later in the component
  methods: {
    async confirmIniciarTratamento() {
      cSwal.cConfirm("Deseja realmente iniciar o tratamento?", async () => {
        await this._iniciarTratamento();
      });
    },
    async _iniciarTratamento() {
      if (!this.dataInicioTratamento || !this.dataPlanejamento || !this.dataPrevista) {
        cSwal.cAlert("É necessário preencher a data de início do tratamento, a data de planejamento e a data prevista para iniciar o tratamento.");
        return;
      }

      cSwal.loading("Iniciando tratamento...");
      const iniciar = await iniciarTratamento(
        this.paciente.id,
        this.dataInicioTratamento,
        this.dataPlanejamento,
        this.dataPrevista,
      );
      cSwal.loaded();

      if (iniciar) {
        cSwal.cSuccess("O início do tratamento foi definido com sucesso.");
        this.$emit("pacienteChange");
        // document.getElementById('closeInicioTratamentoModal').click();
      } else {
        cSwal.cError("Ocorreu um erro ao iniciar o tratamento.");
      }
    },

    deleteMetaTerapeutica(id) {
      cSwal.cConfirm("Deseja realmente excluir esta meta terapêutica?", async () => {
        cSwal.loading("Excluindo...");
        const del = await excluirMetaTerapeutica(id);
        cSwal.loaded();

        if (del) {
          this.$emit("pacienteChange");
          document.querySelector("button.btn-close").click();
          this.novaMetaTerapeutica = "";
          cSwal.cSuccess('Meta terapêutica excluída.');
        } else {
          cSwal.cError("Ocorreu um erro ao excluir a meta terapêutica.");
        }
      });
    },

    async _adicionarMetaTerapeutica() {
      cSwal.loading("Adicionando meta terapêutica...");
      const add = await adicionarMetaTerapeutica(
        this.paciente.id,
        this.novaMetaTerapeutica
      );
      cSwal.loaded();

      if (add) {
        this.$refs.closeModalNovaMetaTerapeutica.click();
        cSwal.cSuccess('Meta terapêutica adicionada.');
        this.$emit("pacienteChange");
        document.querySelector(".modal-header .btn-close").click();
        this.novaMetaTerapeutica = "";
      } else {
        cSwal.cError("Ocorreu um erro ao adicionar a meta terapêutica.");
      }
    },

    async atualizarStatusMeta(id, status) {
      let mensagem = "";
      if (status === "CONCLUIDA") {
        mensagem = "Marcando meta como concluída...";
      } else if (status === "NAO_CONCLUIDA") {
        mensagem = "Marcando meta como não concluída...";
      } else if (status === "OUTRA_AREA") {
        mensagem = "Marcando meta como dependente de outra área...";
      } else {
        mensagem = "Atualizando status da meta...";
      }

      cSwal.loading(mensagem);
      const result = await updateMetaStatus(id, status);
      cSwal.loaded();

      if (result) {
        let successMessage = "";
        if (status === "CONCLUIDA") {
          successMessage = "Meta marcada como concluída.";
        } else if (status === "NAO_CONCLUIDA") {
          successMessage = "Meta marcada como não concluída.";
        } else if (status === "OUTRA_AREA") {
          successMessage = "Meta marcada como dependente de outra área.";
        } else {
          successMessage = "Status da meta atualizado.";
        }
        cSwal.cSuccess(successMessage);
        this.$emit("pacienteChange");
      } else {
        cSwal.cError("Ocorreu um erro ao atualizar o status da meta.");
      }
    },

    save(section) {
      switch (section) {
        case "fasesTratamento":
          this.saveFasesTratamento();
          break;
        case "aparatologia":
          this.salvarAparatologiaData();
          break;
        case "metasTerapeuticas":
          this.saveMetasTerapeuticas();
      }
    },
    saveFasesTratamento() {},
    saveMetasTerapeuticas() {},

    // Métodos para Aparatologia
    carregarAparatologia() {
      // Verificar se o paciente tem dados de aparatologia
      if (this.paciente.aparatologia) {
        // Preencher os dados da aparatologia
        this.aparatologiaData = {
          id: this.paciente.aparatologia.id,
          paciente_id: this.paciente.id,
          aparelho_utilizado: this.paciente.aparatologia.aparelho_utilizado || "",
          contencao_superior: this.paciente.aparatologia.contencao_superior || "",
          contencao_inferior: this.paciente.aparatologia.contencao_inferior || "",
          observacoes: this.paciente.aparatologia.observacoes || ""
        };

        // Atualizar os itens de aparatologia e contenção com os valores do paciente
        this.atualizarItensAparatologia();

        // Fazer backup dos dados originais
        this.aparatologiaOriginal = JSON.parse(JSON.stringify(this.aparatologiaData));
        this.isAparatologiaLoaded = true;
      } else {
        // Se não houver dados, inicializar com valores padrão
        this.inicializarAparatologiaData();
      }
    },

    // Método para carregar a aparatologia com retries
    carregarAparatologiaComRetries(dadosSalvos, tentativa = 1, maxTentativas = 3, intervalo = 1000) {

      // Carregar os dados da aparatologia
      this.carregarAparatologia();

      // Verificar se os dados carregados correspondem aos dados salvos
      const dadosCorrespondem = this.verificarDadosAparatologia(dadosSalvos);

      if (dadosCorrespondem) {
        // Atualizar o backup dos dados originais
        this.aparatologiaOriginal = JSON.parse(JSON.stringify(this.aparatologiaData));

        return;
      }

      // Se não correspondem e ainda temos tentativas, tentar novamente após o intervalo
      if (tentativa < maxTentativas) {
        // Aumentar o intervalo a cada tentativa
        const novoIntervalo = intervalo * 1.5;

        setTimeout(() => {
          this.carregarAparatologiaComRetries(dadosSalvos, tentativa + 1, maxTentativas, novoIntervalo);
        }, novoIntervalo);
      } else {
        console.warn("Número máximo de tentativas atingido. Os dados da aparatologia foram carregados, mas podem não refletir exatamente o que foi salvo.");

        // Se temos um ID, consideramos que o carregamento foi parcialmente bem-sucedido
        if (this.paciente.aparatologia && this.paciente.aparatologia.id) {

          // Atualizar o backup dos dados originais
          this.aparatologiaOriginal = JSON.parse(JSON.stringify(this.aparatologiaData));
        }
      }
    },

    // Método para verificar se os dados da aparatologia correspondem aos dados salvos
    verificarDadosAparatologia(dadosSalvos) {
      // Se não temos aparatologia carregada, retornar false
      if (!this.paciente.aparatologia) {
        return false;
      }

      // Se encontramos um ID, consideramos que a aparatologia foi carregada com sucesso
      if (this.paciente.aparatologia.id) {
        console.log("ID da aparatologia encontrado:", this.paciente.aparatologia.id);

        // Verificar se o aparelho utilizado corresponde (campo mais importante)
        const aparelhoCorresponde = this.compararValores(
          this.paciente.aparatologia.aparelho_utilizado,
          dadosSalvos.aparelho_utilizado
        );

        // Se pelo menos o aparelho corresponde, consideramos sucesso
        if (aparelhoCorresponde) {
          console.log("Aparelho utilizado corresponde. Considerando carregamento bem-sucedido.");
          return true;
        }

        // Registrar informações para depuração
        console.log("Aparelho utilizado não corresponde:", {
          salvo: dadosSalvos.aparelho_utilizado,
          carregado: this.paciente.aparatologia.aparelho_utilizado
        });
      }

      // Verificação completa (usada apenas para depuração)
      const aparelhoCorresponde = this.compararValores(
        this.paciente.aparatologia.aparelho_utilizado,
        dadosSalvos.aparelho_utilizado
      );
      const contencaoSuperiorCorresponde = this.compararValores(
        this.paciente.aparatologia.contencao_superior,
        dadosSalvos.contencao_superior
      );
      const contencaoInferiorCorresponde = this.compararValores(
        this.paciente.aparatologia.contencao_inferior,
        dadosSalvos.contencao_inferior
      );
      const observacoesCorrespondem = this.compararValores(
        this.paciente.aparatologia.observacoes,
        dadosSalvos.observacoes
      );

      // Verificar se todos os campos correspondem
      const todosCorrespondem = aparelhoCorresponde && contencaoSuperiorCorresponde && contencaoInferiorCorresponde && observacoesCorrespondem;

      console.log("Verificação detalhada de dados da aparatologia:", {
        aparelhoCorresponde,
        contencaoSuperiorCorresponde,
        contencaoInferiorCorresponde,
        observacoesCorrespondem,
        todosCorrespondem
      });

      return false;
    },

    // Método auxiliar para comparar valores, tratando null e undefined como equivalentes a string vazia
    compararValores(valor1, valor2) {
      // Normalizar valores nulos ou undefined para string vazia
      const v1 = valor1 === null || valor1 === undefined ? "" : String(valor1);
      const v2 = valor2 === null || valor2 === undefined ? "" : String(valor2);

      return v1 === v2;
    },

    inicializarAparatologiaData() {
      // Inicializar com valores vazios
      this.aparatologiaData = {
        id: null,
        paciente_id: this.paciente.id,
        aparelho_utilizado: "",
        contencao_superior: "",
        contencao_inferior: "",
        observacoes: ""
      };

      // Atualizar os itens de aparatologia e contenção com os valores vazios
      this.atualizarItensAparatologia();

      // Fazer backup dos dados originais
      this.aparatologiaOriginal = JSON.parse(JSON.stringify(this.aparatologiaData));
      this.isAparatologiaLoaded = true;
    },

    atualizarItensAparatologia() {
      // Atualizar o item de aparelho utilizado
      const aparelhoItem = this.aparatologiaItems.find(item => item.name === "Aparelho utilizado");
      if (aparelhoItem) {
        aparelhoItem.text = this.aparatologiaData.aparelho_utilizado;
      }

      // Atualizar o item de contenção superior
      const contencaoSuperiorItem = this.contencaoItems.find(item => item.name === "Contenção superior");
      if (contencaoSuperiorItem) {
        contencaoSuperiorItem.text = this.aparatologiaData.contencao_superior;
      }

      // Atualizar o item de contenção inferior
      const contencaoInferiorItem = this.contencaoItems.find(item => item.name === "Contenção inferior");
      if (contencaoInferiorItem) {
        contencaoInferiorItem.text = this.aparatologiaData.contencao_inferior;
      }
    },

    prepararDadosAparatologia() {
      // Obter os valores atuais dos itens
      const aparelhoItem = this.aparatologiaItems.find(item => item.name === "Aparelho utilizado");
      const contencaoSuperiorItem = this.contencaoItems.find(item => item.name === "Contenção superior");
      const contencaoInferiorItem = this.contencaoItems.find(item => item.name === "Contenção inferior");

      // Atualizar o objeto de dados
      this.aparatologiaData.aparelho_utilizado = aparelhoItem ? aparelhoItem.text : "";
      this.aparatologiaData.contencao_superior = contencaoSuperiorItem ? contencaoSuperiorItem.text : "";
      this.aparatologiaData.contencao_inferior = contencaoInferiorItem ? contencaoInferiorItem.text : "";
      this.aparatologiaData.paciente_id = this.paciente.id;

      return this.aparatologiaData;
    },

    async salvarAparatologiaData() {
      // Preparar os dados para salvar
      const dadosAparatologia = this.prepararDadosAparatologia();

      // Verificar se há alterações
      if (!this.hasAparatologiaChanges) {
        // Cancelar a edição de ambas as seções
        this.cancelarEdicaoAparatologia();
        return;
      }

      cSwal.loading("Salvando dados da aparatologia...");

      try {
        let response;

        if (this.aparatologiaData.id) {
          // Atualizar registro existente
          response = await atualizarAparatologia(this.aparatologiaData.id, dadosAparatologia);
        } else {
          this.aparatologiaData.id = true;

          // Criar novo registro
          response = await salvarAparatologia(this.paciente.id, dadosAparatologia);

          // Como o back-end não retorna o ID, vamos atualizar o paciente para obter os dados atualizados
          this.$emit("pacienteChange");

          // Aguardar o próximo ciclo de renderização para garantir que o paciente foi atualizado
          this.$nextTick(() => {
            // Tentar carregar a aparatologia com retries
            this.carregarAparatologiaComRetries(dadosAparatologia);
          });
        }

        // Fechar o modal de loading antes de mostrar o sucesso/erro
        cSwal.loaded();

        if (response) {
          // Atualizar o backup dos dados originais
          this.aparatologiaOriginal = JSON.parse(JSON.stringify(this.aparatologiaData));

          // Desativar o modo de edição de ambas as seções
          this.cancelarEdicaoAparatologia();

          cSwal.cSuccess("Dados da aparatologia salvos com sucesso!");

          // Se estamos atualizando um registro existente, notificar o componente pai
          if (this.aparatologiaData.id) {
            // Notificar o componente pai para atualizar o paciente
            this.$emit("pacienteChange");
          }
        } else {
          cSwal.cError("Ocorreu um erro ao salvar os dados da aparatologia.");
        }
      } catch (error) {
        // Fechar o modal de loading antes de mostrar o erro
        cSwal.loaded();
        console.error("Erro ao salvar aparatologia:", error);
        cSwal.cError("Ocorreu um erro ao salvar os dados da aparatologia.");
      }
    },
    toggleEditMode(section) {
      if (section === 'necessidadesEncaminhamento') {
        if (!this.isEditing[section]) {
          // Entrando no modo de edição - fazer backup das necessidades originais
          this.necessidadesOriginais = JSON.parse(JSON.stringify(this.necessidadesEncaminhamentos));

          // Ativar o modo de edição para ocultar o botão Voltar
          this.editModeActive = true;
          // Emitir evento para o componente pai
          this.$emit('edit-mode-active', true);
        } else {
          // Saindo do modo de edição sem salvar - restaurar as necessidades originais
          // Emitir evento para atualizar o componente pai
          this.$emit('update:necessidades-encaminhamentos', JSON.parse(JSON.stringify(this.necessidadesOriginais)));

          // Desativar o modo de edição para mostrar o botão Voltar
          this.editModeActive = false;
          // Emitir evento para o componente pai
          this.$emit('edit-mode-active', false);
        }
      } else if (section === 'aparatologia' || section === 'contencao') {
        if (!this.isEditing[section]) {
          // Verificar se os dados da aparatologia foram carregados
          if (!this.isAparatologiaLoaded) {
            this.carregarAparatologia();
          }

          // Ativar o modo de edição apenas para a seção clicada
          this.isEditing[section] = true;

          // Ativar o modo de edição para ocultar o botão Voltar
          this.editModeActive = true;
          // Emitir evento para o componente pai
          this.$emit('edit-mode-active', true);

          return; // Não precisamos alterar o isEditing[section] novamente, já fizemos isso acima
        }
      }

      this.isEditing[section] = !this.isEditing[section];

      // Resetar o formulário de nova necessidade quando entrar/sair do modo de edição
      if (section === 'necessidadesEncaminhamento') {
        this.resetNovaNecessidade();
      }
    },

    textNewLine(descricao) {
      return descricao.replaceAll("\\n", "&#13;&#10;");
    },

    formatarStatus(status) {
      switch (status) {
        case 'PENDENTE':
          return 'Pendente';
        case 'CONCLUIDA':
          return 'Concluída';
        case 'NAO_CONCLUIDA':
          return 'Não concluída';
        default:
          return status;
      }
    },

    // Método para ativar a edição de ambas as seções no mobile
    ativarEdicaoMobile() {
      // Verificar se os dados da aparatologia foram carregados
      if (!this.isAparatologiaLoaded) {
        this.carregarAparatologia();
      }

      // Ativar o modo de edição para ambas as seções
      this.isEditing['aparatologia'] = true;
      this.isEditing['contencao'] = true;

      // Ativar o modo de edição para ocultar o botão Voltar
      this.editModeActive = true;
      // Emitir evento para o componente pai
      this.$emit('edit-mode-active', true);
    },

    // Método para cancelar a edição de aparatologia e contenção
    cancelarEdicaoAparatologia(section = null) {
      // Restaurar os dados originais
      if (this.aparatologiaOriginal) {
        this.aparatologiaData = JSON.parse(JSON.stringify(this.aparatologiaOriginal));
        this.atualizarItensAparatologia();
      }

      // Se nenhuma seção for especificada, cancelar ambas
      if (!section) {
        this.isEditing['aparatologia'] = false;
        this.isEditing['contencao'] = false;
      } else {
        // Cancelar apenas a seção especificada
        this.isEditing[section] = false;
      }

      // Verificar se ainda há alguma seção em modo de edição
      const algumaModoEdicao = this.isEditing['aparatologia'] || this.isEditing['contencao'];

      // Só desativar o modo de edição se não houver nenhuma seção em edição
      if (!algumaModoEdicao) {
        // Desativar o modo de edição para mostrar o botão Voltar
        this.editModeActive = false;
        // Emitir evento para o componente pai
        this.$emit('edit-mode-active', false);
      }
    },

    resetNovaNecessidade() {
      this.novaNecessidade = {
        especialista: "",
        necessidade: "",
        observacoes: "",
        status: "PENDENTE"
      };
    },

    async adicionarNecessidade() {
      if (!this.isNovaNecessidadeValida) {
        cSwal.cAlert("Por favor, preencha os campos obrigatórios: Especialista e Necessidade.");
        return;
      }

      cSwal.loading("Adicionando necessidade de encaminhamento...");

      try {
        const response = await adicionarNecessidadeEncaminhamento(
          this.paciente.id,
          this.novaNecessidade
        );

        // Fechar o modal de loading antes de mostrar o sucesso/erro
        cSwal.loaded();

        if (response) {
          // Fechar o modal
          this.$refs.closeModalNecessidadeEncaminhamento.click();

          // Criar uma cópia das necessidades atuais
          const necessidadesAtualizadas = [...this.necessidadesEncaminhamentos];

          // Adicionar a nova necessidade com o ID retornado da API
          const novaNecessidadeComId = {
            ...this.novaNecessidade,
            id: response.data.id
          };

          necessidadesAtualizadas.push(novaNecessidadeComId);

          // Emitir evento para atualizar o componente pai
          this.$emit('update:necessidades-encaminhamentos', necessidadesAtualizadas);

          // Atualizar o backup das necessidades originais
          this.necessidadesOriginais = JSON.parse(JSON.stringify(necessidadesAtualizadas));

          // Resetar o formulário
          this.resetNovaNecessidade();

          // Notificar o componente pai para atualizar o paciente
          this.$emit("pacienteChange");

          // Sair do modo de edição após adicionar
          this.toggleEditMode('necessidadesEncaminhamento');

          cSwal.cSuccess("Necessidade de encaminhamento adicionada com sucesso!");
        } else {
          cSwal.cError("Ocorreu um erro ao adicionar a necessidade de encaminhamento.");
        }
      } catch (error) {
        // Fechar o modal de loading antes de mostrar o erro
        cSwal.loaded();
        console.error("Erro ao adicionar necessidade:", error);
        cSwal.cError("Ocorreu um erro ao adicionar a necessidade de encaminhamento.");
      }
    },

    async excluirNecessidade(id) {
      cSwal.cConfirm("Deseja realmente excluir esta necessidade de encaminhamento?", async () => {
        cSwal.loading("Excluindo necessidade de encaminhamento...");

        try {
          const response = await excluirNecessidadeEncaminhamento(id);

          // Fechar o modal de loading antes de mostrar o sucesso/erro
          cSwal.loaded();

          if (response) {
            // Criar uma cópia das necessidades atuais e filtrar a necessidade excluída
            const necessidadesAtualizadas = this.necessidadesEncaminhamentos.filter(
              necessidade => necessidade.id !== id
            );

            // Emitir evento para atualizar o componente pai
            this.$emit('update:necessidades-encaminhamentos', necessidadesAtualizadas);

            // Atualizar o backup das necessidades originais
            this.necessidadesOriginais = JSON.parse(JSON.stringify(necessidadesAtualizadas));

            // Notificar o componente pai para atualizar o paciente
            this.$emit("pacienteChange");

            cSwal.cSuccess("Necessidade de encaminhamento excluída com sucesso!");
          } else {
            cSwal.cError("Ocorreu um erro ao excluir a necessidade de encaminhamento.");
          }
        } catch (error) {
          // Fechar o modal de loading antes de mostrar o erro
          cSwal.loaded();
          console.error("Erro ao excluir necessidade:", error);
          cSwal.cError("Ocorreu um erro ao excluir a necessidade de encaminhamento.");
        }
      });
    },

    async salvarNecessidadesEncaminhamento() {
      if (!this.necessidadesEncaminhamentos || this.necessidadesEncaminhamentos.length === 0) {
        this.toggleEditMode('necessidadesEncaminhamento');
        return;
      }

      cSwal.loading("Salvando necessidades de encaminhamento...");

      try {
        // Criar um array de promessas para atualizar todas as necessidades
        const promises = this.necessidadesEncaminhamentos.map(necessidade =>
          atualizarNecessidadeEncaminhamento(necessidade.id, necessidade)
        );

        // Executar todas as promessas
        const results = await Promise.all(promises);

        // Fechar o modal de loading antes de mostrar o sucesso/erro
        cSwal.loaded();

        // Verificar se todas as atualizações foram bem-sucedidas
        const allSuccess = results.every(result => result !== false);

        if (allSuccess) {
          // Atualizar o backup das necessidades originais
          this.necessidadesOriginais = JSON.parse(JSON.stringify(this.necessidadesEncaminhamentos));

          // Desativar o modo de edição para mostrar o botão Voltar
          this.editModeActive = false;
          // Emitir evento para o componente pai
          this.$emit('edit-mode-active', false);

          // Sair do modo de edição
          this.toggleEditMode('necessidadesEncaminhamento');

          // Notificar o componente pai para atualizar o paciente
          this.$emit("pacienteChange");

          cSwal.cSuccess("Necessidades de encaminhamento salvas com sucesso!");
        } else {
          cSwal.cError("Ocorreu um erro ao salvar algumas necessidades de encaminhamento.");
        }
      } catch (error) {
        // Fechar o modal de loading antes de mostrar o erro
        cSwal.loaded();
        console.error("Erro ao salvar necessidades:", error);
        cSwal.cError("Ocorreu um erro ao salvar as necessidades de encaminhamento.");
      }
    },

    handleGlobalDragEnd() {
      // Garantir que a zona de exclusão seja escondida se o drag terminar fora dos eventos normais
      this.isDraggingMeta = false;
      this.isOverDeleteZone = false;
      this.wasDroppedOnDeleteZone = false;

      // Remover classe de destaque da zona de exclusão
      if (this.$refs.deleteZone) {
        this.$refs.deleteZone.classList.remove('highlight');
      }

      // Limpar eventos da zona de exclusão
      this.cleanupDeleteZoneEvents();
    },

    onDragStart(evt) {
      // Resetar flags para o novo drag
      this.wasDroppedOnDeleteZone = false;
      this.isOverDeleteZone = false;

      // Mostrar a zona de exclusão quando começar a arrastar
      this.isDraggingMeta = true;

      // Obter o ID da meta a partir do elemento arrastado
      const metaId = evt.item.__draggable_context?.element?.id;

      // Verificar se o ID é válido
      if (metaId) {
        this.currentDraggedMetaId = metaId;
        console.log('Meta ID arrastada:', metaId);

        // Configurar o dataTransfer para permitir o drop
        if (evt.originalEvent && evt.originalEvent.dataTransfer) {
          evt.originalEvent.dataTransfer.effectAllowed = 'move';

          // Armazenar o ID da meta no dataTransfer
          evt.originalEvent.dataTransfer.setData('text/plain', metaId);
        }

        // Configurar a zona de exclusão
        this.setupDeleteZoneDetection();

        // Garantir que a zona de exclusão esteja visível
        setTimeout(() => {
          if (this.$refs.deleteZone) {
            this.$refs.deleteZone.style.zIndex = '9999';
          }
        }, 0);
      } else {
        console.error('ID da meta não encontrado no início do drag:', evt.item);
      }
    },

    setupDeleteZoneDetection() {
      // Adicionar evento para detectar quando o elemento está sobre a zona de exclusão
      const deleteZone = this.$refs.deleteZone;
      if (!deleteZone) return;

      // Adicionar eventos para detectar quando o elemento está sobre a zona de exclusão
      deleteZone.addEventListener('dragover', this.onDragOverDeleteZone);
      deleteZone.addEventListener('dragleave', this.onDragLeaveDeleteZone);
      deleteZone.addEventListener('drop', this.onDropOnDeleteZone);

      // Adicionar eventos para o documento inteiro para garantir que o drop funcione
      document.addEventListener('dragover', this.onDocumentDragOver);
      document.addEventListener('drop', this.onDocumentDrop);
    },

    onDocumentDragOver(event) {
      // Permitir o drop em qualquer lugar do documento
      event.preventDefault();

      // Verificar se o mouse está sobre a zona de exclusão
      if (this.$refs.deleteZone) {
        const deleteZoneRect = this.$refs.deleteZone.getBoundingClientRect();
        const isOverDeleteZone =
          event.clientX >= deleteZoneRect.left &&
          event.clientX <= deleteZoneRect.right &&
          event.clientY >= deleteZoneRect.top &&
          event.clientY <= deleteZoneRect.bottom;

        // Se o mouse estiver sobre a zona de exclusão, destacá-la
        if (isOverDeleteZone && !this.isOverDeleteZone) {
          this.onDragOverDeleteZone(event);
        } else if (!isOverDeleteZone && this.isOverDeleteZone) {
          this.onDragLeaveDeleteZone();
        }
      }
    },

    onDocumentDrop(event) {
      // Prevenir o comportamento padrão do drop
      event.preventDefault();

      // Verificar se o drop ocorreu sobre a zona de exclusão
      if (this.$refs.deleteZone) {
        const deleteZoneRect = this.$refs.deleteZone.getBoundingClientRect();
        const isOverDeleteZone =
          event.clientX >= deleteZoneRect.left &&
          event.clientX <= deleteZoneRect.right &&
          event.clientY >= deleteZoneRect.top &&
          event.clientY <= deleteZoneRect.bottom;

        // Se o drop ocorreu sobre a zona de exclusão, chamar o método de exclusão
        if (isOverDeleteZone) {
          this.onDropOnDeleteZone(event);
        }
      }
    },

    cleanupDeleteZoneEvents() {
      // Remover eventos quando o drag terminar
      const deleteZone = this.$refs.deleteZone;
      if (!deleteZone) return;

      deleteZone.removeEventListener('dragover', this.onDragOverDeleteZone);
      deleteZone.removeEventListener('dragleave', this.onDragLeaveDeleteZone);
      deleteZone.removeEventListener('drop', this.onDropOnDeleteZone);

      // Remover eventos do documento
      document.removeEventListener('dragover', this.onDocumentDragOver);
      document.removeEventListener('drop', this.onDocumentDrop);
    },

    onDragOverDeleteZone(event) {
      event.preventDefault();
      // Destacar a zona de exclusão quando o elemento estiver sobre ela
      this.$refs.deleteZone.classList.add('highlight');
      this.isOverDeleteZone = true;

      // Garantir que o drop seja permitido
      if (event.dataTransfer) {
        event.dataTransfer.dropEffect = 'move';
      }
    },

    onDragLeaveDeleteZone() {
      // Remover destaque quando o elemento sair da zona de exclusão
      this.$refs.deleteZone.classList.remove('highlight');
      this.isOverDeleteZone = false;
    },

    onDropOnDeleteZone(event) {
      event.preventDefault();
      event.stopPropagation();

      // Marcar que o item foi solto na zona de exclusão
      this.wasDroppedOnDeleteZone = true;
      console.log('Item solto na zona de exclusão, wasDroppedOnDeleteZone =', this.wasDroppedOnDeleteZone);

      // Remover destaque
      this.$refs.deleteZone.classList.remove('highlight');
      this.isOverDeleteZone = false;

      // Obter o ID da meta do dataTransfer ou usar o currentDraggedMetaId
      let metaId = this.currentDraggedMetaId;

      try {
        if (event.dataTransfer && event.dataTransfer.getData('text/plain')) {
          const transferData = event.dataTransfer.getData('text/plain');
          if (transferData && !isNaN(parseInt(transferData))) {
            metaId = parseInt(transferData);
          }
        }
      } catch (error) {
        console.error('Erro ao obter dados do dataTransfer:', error);
      }

      console.log('Meta ID para exclusão:', metaId);

      // Confirmar exclusão da meta
      if (metaId) {
        // Confirmar antes de excluir
        cSwal.cConfirm("Deseja realmente excluir esta meta terapêutica?", () => {
          // Executar a exclusão diretamente
          cSwal.loading("Excluindo...");
          excluirMetaTerapeutica(metaId).then(del => {
            cSwal.loaded();
            if (del) {
              this.$emit("pacienteChange");
              cSwal.cSuccess('Meta terapêutica excluída.');
            } else {
              cSwal.cError("Ocorreu um erro ao excluir a meta terapêutica.");
            }
          });
        });
      } else {
        console.error('ID da meta não encontrado para exclusão');
        cSwal.cError('Erro ao obter o ID da meta. Por favor, tente novamente.');
        this.$emit("pacienteChange");
      }

      // Limpar eventos
      this.cleanupDeleteZoneEvents();
    },

    onDragEnd(evt) {
      console.log('onDragEnd chamado, wasDroppedOnDeleteZone =', this.wasDroppedOnDeleteZone);

      // Se foi solto na zona de exclusão, não prosseguir com a alteração de status
      if (this.wasDroppedOnDeleteZone) {
        console.log('Item foi solto na zona de exclusão, cancelando alteração de status');
        // Resetar a flag para o próximo drag
        this.wasDroppedOnDeleteZone = false;
        // Esconder a zona de exclusão
        this.isDraggingMeta = false;
        this.isOverDeleteZone = false;
        // Limpar eventos da zona de exclusão
        this.cleanupDeleteZoneEvents();
        return;
      }

      // Esconder a zona de exclusão quando terminar de arrastar
      this.isDraggingMeta = false;
      this.isOverDeleteZone = false;

      // Limpar eventos da zona de exclusão
      this.cleanupDeleteZoneEvents();

      // Obter o ID da meta a partir do elemento arrastado
      // Quando usamos draggable, o elemento arrastado é um clone do original
      // e precisamos obter o ID da meta a partir do modelo de dados
      const metaId = evt.item.__draggable_context?.element?.id;

      if (!metaId) {
        console.error('ID da meta não encontrado:', evt.item);
        cSwal.cError('Erro ao obter o ID da meta. Por favor, tente novamente.');
        this.$emit("pacienteChange");
        return;
      }

      // Identificar para qual quadrante a meta foi arrastada
      const targetParentElement = evt.to.parentElement;
      const sourceParentElement = evt.from.parentElement;

      // Verificar se a meta foi solta no mesmo quadrante de origem
      if (targetParentElement === sourceParentElement) {
        // Não fazer nada se a meta foi solta no mesmo quadrante
        return;
      }

      // Determinar o novo status com base no quadrante de destino
      let novoStatus = 'PENDENTE';
      let confirmMessage = 'Deseja realmente alterar o status desta meta?';

      // Verificar o tipo de quadrante de destino
      if (targetParentElement.classList.contains('concluidas')) {
        novoStatus = 'CONCLUIDA';
        confirmMessage = 'Deseja marcar esta meta como concluída?';
      } else if (targetParentElement.classList.contains('nao-concluidas')) {
        novoStatus = 'NAO_CONCLUIDA';
        confirmMessage = 'Deseja marcar esta meta como não concluída?';
      } else if (targetParentElement.classList.contains('outra-area')) {
        novoStatus = 'OUTRA_AREA';
        confirmMessage = 'Deseja marcar esta meta como dependente de outra área?';
      } else if (targetParentElement.classList.contains('pendentes')) {
        novoStatus = 'PENDENTE';
        confirmMessage = 'Deseja marcar esta meta como pendente?';
      } else {
        // Se não conseguir identificar o quadrante, exibir um log e cancelar a operação
        console.error('Quadrante de destino não identificado:', targetParentElement);
        cSwal.cError('Erro ao identificar o quadrante de destino. Por favor, tente novamente.');
        this.$emit("pacienteChange");
        return;
      }

      // Confirmar a alteração do status
      cSwal.cConfirm(confirmMessage, async () => {
        await this.atualizarStatusMeta(metaId, novoStatus);
      }, () => {
        // Se o usuário cancelar, recarregar a página para reverter o drag
        this.$emit("pacienteChange");
      });
    },

    // Método para lidar com o redimensionamento da janela
    handleResize() {
      // Não é necessário fazer nada aqui, pois o computed property isMobile
      // será recalculado automaticamente quando window.innerWidth mudar
    },

    // Método para configurar o Intersection Observer para a seção de necessidades
    setupNecessidadesVisibilityObserver() {
      // Criar um novo Intersection Observer
      this.necessidadesObserver = new IntersectionObserver((entries) => {
        // Verificar se a seção de necessidades está visível
        const isVisible = entries[0].isIntersecting;
        this.isNecessidadesSectionInView = isVisible;
      }, {
        // Configurações do observer
        threshold: 0.1, // 10% da seção visível é suficiente para considerá-la visível
        rootMargin: '0px 0px 100px 0px' // Margem adicional para detectar quando está próximo
      });

      // Observar a seção de necessidades usando a ref
      if (this.$refs.necessidadesSection) {
        this.necessidadesObserver.observe(this.$refs.necessidadesSection);
      }
    },

    // Método para configurar o Intersection Observer para a seção de aparatologia
    setupAparatologiaVisibilityObserver() {
      // Criar um novo Intersection Observer
      this.aparatologiaObserver = new IntersectionObserver((entries) => {
        // Verificar se alguma das seções está visível
        const isVisible = entries.some(entry => entry.isIntersecting);
        this.isAparatologiaSectionInView = isVisible;
      }, {
        // Configurações do observer
        threshold: 0.1, // 10% da seção visível é suficiente para considerá-la visível
        rootMargin: '0px 0px 100px 0px' // Margem adicional para detectar quando está próximo
      });

      // Observar as seções de aparatologia e contenção usando as refs
      if (this.$refs.aparatologiaBox) {
        this.aparatologiaObserver.observe(this.$refs.aparatologiaBox);
      }

      if (this.$refs.contencaoBox) {
        this.aparatologiaObserver.observe(this.$refs.contencaoBox);
      }
    },
  },
  computed: {
    isMobile() {
      return window.innerWidth < 768; // sm breakpoint
    },
    isNovaNecessidadeValida() {
      return this.novaNecessidade.especialista && this.novaNecessidade.necessidade;
    },
    isNecessidadesVisible() {
      return this.isNecessidadesSectionInView;
    },
    isAparatologiaVisible() {
      return this.isAparatologiaSectionInView;
    },
    hasNecessidadesChanges() {
      // Se não houver necessidades originais ou atuais, não há mudanças
      if (!this.necessidadesOriginais || !this.necessidadesEncaminhamentos) {
        return false;
      }

      // Se o número de necessidades for diferente, houve mudanças
      if (this.necessidadesOriginais.length !== this.necessidadesEncaminhamentos.length) {
        return true;
      }

      // Comparar cada necessidade para verificar se houve mudanças
      for (let i = 0; i < this.necessidadesEncaminhamentos.length; i++) {
        const atual = this.necessidadesEncaminhamentos[i];
        const original = this.necessidadesOriginais.find(n => n.id === atual.id);

        // Se não encontrar a necessidade original, houve mudanças
        if (!original) {
          return true;
        }

        // Comparar os campos relevantes
        if (
          atual.especialista !== original.especialista ||
          atual.necessidade !== original.necessidade ||
          atual.observacoes !== original.observacoes ||
          atual.status !== original.status
        ) {
          return true;
        }
      }

      // Se chegou até aqui, não houve mudanças
      return false;
    },
    hasAparatologiaChanges() {
      // Se não houver dados originais, não há mudanças
      if (!this.aparatologiaOriginal || !this.aparatologiaData) {
        return false;
      }

      // Preparar os dados atuais
      const dadosAtuais = this.prepararDadosAparatologia();

      // Comparar os campos relevantes
      return (
        dadosAtuais.aparelho_utilizado !== this.aparatologiaOriginal.aparelho_utilizado ||
        dadosAtuais.contencao_superior !== this.aparatologiaOriginal.contencao_superior ||
        dadosAtuais.contencao_inferior !== this.aparatologiaOriginal.contencao_inferior ||
        dadosAtuais.observacoes !== this.aparatologiaOriginal.observacoes
      );
    },
    dataPrevista() {
      if (!this.dataInicioTratamento || !this.terminoPrevistoMeses) return "";
      const planejamentoDate = new Date(this.dataInicioTratamento);
      if (isNaN(planejamentoDate)) return "";
      const resultDate = new Date(planejamentoDate);
      resultDate.setMonth(resultDate.getMonth() + Number(this.terminoPrevistoMeses));
      return this.$filters.dateYmd(resultDate);
    },
    ultimaFase() {
      return this.paciente.fases_tratamento ? this.paciente.fases_tratamento[this.paciente.fases_tratamento.length - 1] : null;
    },
    metasPendentes: {
      get() {
        if (!this.paciente.metas_terapeuticas) return [];
        return this.paciente.metas_terapeuticas.filter(meta => meta.status === 'PENDENTE' || !meta.status);
      },
      set() {
        // Não precisamos fazer nada aqui, pois o onDragEnd vai lidar com as mudanças
      }
    },
    metasConcluidas: {
      get() {
        if (!this.paciente.metas_terapeuticas) return [];
        return this.paciente.metas_terapeuticas.filter(meta => meta.status === 'CONCLUIDA');
      },
      set() {
        // Não precisamos fazer nada aqui, pois o onDragEnd vai lidar com as mudanças
      }
    },
    metasNaoConcluidas: {
      get() {
        if (!this.paciente.metas_terapeuticas) return [];
        return this.paciente.metas_terapeuticas.filter(meta => meta.status === 'NAO_CONCLUIDA');
      },
      set() {
        // Não precisamos fazer nada aqui, pois o onDragEnd vai lidar com as mudanças
      }
    },
    metasOutraArea: {
      get() {
        if (!this.paciente.metas_terapeuticas) return [];
        return this.paciente.metas_terapeuticas.filter(meta => meta.status === 'OUTRA_AREA');
      },
      set() {
        // Não precisamos fazer nada aqui, pois o onDragEnd vai lidar com as mudanças
      }
    },
    // Computed property for necessidades_encaminhamentos to avoid mutating props
    necessidadesEncaminhamentos: {
      get() {
        return this.paciente.necessidades_encaminhamentos || [];
      },
      set(value) {
        // We'll emit an event to update the parent component
        this.$emit('update:necessidades-encaminhamentos', value);
        // We'll also update our local copy
        this.necessidadesOriginais = JSON.parse(JSON.stringify(value));
      }
    }
  },
  components: {
    MaterialInput,
    draggable
  },
  mounted() {
    this.$refs.modalMetaTerapeutica.addEventListener("shown.bs.modal", () => {
      this.$refs.metaTerapeutica.getInput().focus();
    });

    // Inicializar o modal de necessidades de encaminhamento
    this.$refs.modalNecessidadeEncaminhamento.addEventListener("shown.bs.modal", () => {
      // Focar no primeiro campo do formulário
      document.getElementById('especialista').focus();
    });

    // Inicializar o modal de necessidades de encaminhamento
    this.$refs.modalNecessidadeEncaminhamento.addEventListener("hidden.bs.modal", () => {
      // Resetar o formulário quando o modal for fechado
      this.resetNovaNecessidade();
    });

    // Inicializar a zona de exclusão
    window.addEventListener('dragend', this.handleGlobalDragEnd);

    // Adicionar listener para redimensionamento da janela (para detectar mudanças de tamanho de tela)
    window.addEventListener('resize', this.handleResize);

    // Inicializar o backup das necessidades originais
    if (this.paciente.necessidades_encaminhamentos) {
      this.necessidadesOriginais = JSON.parse(JSON.stringify(this.paciente.necessidades_encaminhamentos));
    }

    // Carregar os dados da aparatologia
    this.carregarAparatologia();

    // Configurar os Intersection Observers para detectar quando as seções estão visíveis
    // Usar nextTick para garantir que o DOM esteja completamente renderizado
    this.$nextTick(() => {
      this.setupNecessidadesVisibilityObserver();
      this.setupAparatologiaVisibilityObserver();
    });
  },
  beforeMount() {},
  beforeUnmount() {
    // Limpar eventos ao desmontar o componente
    window.removeEventListener('dragend', this.handleGlobalDragEnd);
    window.removeEventListener('resize', this.handleResize);

    // Desconectar os Intersection Observers
    if (this.necessidadesObserver) {
      this.necessidadesObserver.disconnect();
    }

    if (this.aparatologiaObserver) {
      this.aparatologiaObserver.disconnect();
    }
  },
};
</script>

<style scoped>
/* Estilos para o ícone de edição */
.edit-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-icon-wrapper:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.edit-icon-wrapper.active {
  background-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

.edit-icon {
  color: white;
  font-size: 14px;
}

/* Estilos para o ícone de adicionar */
.add-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-icon-wrapper:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.add-icon {
  color: white;
  font-size: 14px;
}
</style>
