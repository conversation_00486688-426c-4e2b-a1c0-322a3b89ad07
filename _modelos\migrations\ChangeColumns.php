<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $table = 'table_name';

    public function up(): void
    {
        Schema::table($this->table, function (Blueprint $table) {
            $table->string('string_column')->nullable()->change();
            $table->text('text_column');
        });
    }
    
    public function down(): void
    {
        Schema::table($this->table, function (Blueprint $table) {
            $table->string('string_column')->nullable(false)->change();
            $table->dropColumn('text_column');
        });
    }
};