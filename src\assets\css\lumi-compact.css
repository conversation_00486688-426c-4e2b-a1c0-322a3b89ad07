/*
 * Lumi Compact CSS - Otimização de espaço e elementos
 * Este arquivo sobrescreve estilos do Bootstrap para criar uma interface mais compacta
 * mantendo a elegância e profissionalismo do design
 */

/* ===== VARIÁVEIS ===== */
:root {
  --lumi-input-height: calc(1.6em + 0.6rem + 2px);
  --lumi-input-padding: 0.2rem 0.4rem;
  --lumi-font-size-sm: 0.8rem;
  --lumi-font-size-base: 0.85rem;
  --lumi-spacing-xs: 0.15rem;
  --lumi-spacing-sm: 0.25rem;
  --lumi-spacing-md: 0.5rem;
  --lumi-spacing-lg: 0.75rem;

  /* Breakpoints personalizados */
  --lumi-breakpoint-xs: 0;
  --lumi-breakpoint-sm: 576px;
  --lumi-breakpoint-md: 768px;
  --lumi-breakpoint-lg: 992px;
  --lumi-breakpoint-xl: 1200px;
  --lumi-breakpoint-xxl: 1400px;
}

/* ===== TIPOGRAFIA ===== */
body {
  font-size: var(--lumi-font-size-base);
}

.form-label, .input-group-text, .form-text {
  font-size: var(--lumi-font-size-sm);
  margin-bottom: var(--lumi-spacing-xs);
}

/* ===== FORMULÁRIOS ===== */
/* Inputs e campos de formulário */
.form-control,
.form-select {
  height: var(--lumi-input-height);
  padding: var(--lumi-input-padding);
  font-size: var(--lumi-font-size-base);
}

/* Tamanhos padronizados para inputs */
.input-sm {
  height: calc(1.3em + 0.3rem + 2px);
  padding: 0.15rem 0.3rem;
  font-size: 0.75rem;
}

.input-md {
  height: calc(1.4em + 0.4rem + 2px);
  padding: 0.2rem 0.4rem;
  font-size: 0.85rem;
}

.input-lg {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.9rem;
}

textarea.form-control {
  height: auto;
  min-height: calc(var(--lumi-input-height) * 2);
}

.input-group-text {
  padding: var(--lumi-input-padding);
  height: var(--lumi-input-height);
}

/* Ajuste para labels */
.form-label {
  margin-bottom: var(--lumi-spacing-xs);
}

/* Ajuste para grupos de formulário */
.form-group, .mb-3 {
  margin-bottom: var(--lumi-spacing-md) !important;
}

/* Formulários responsivos */
@media (max-width: 767.98px) {
  /* Ajuste para formulários em dispositivos móveis */
  .form-responsive .row {
    margin-left: -0.15rem;
    margin-right: -0.15rem;
  }

  .form-responsive .row > [class*="col-"] {
    padding-left: 0.15rem;
    padding-right: 0.15rem;
  }

  /* Permitir que inputs de data, número, etc ocupem menos espaço */
  .form-responsive .form-control[type="date"],
  .form-responsive .form-control[type="time"],
  .form-responsive .form-control[type="number"],
  .form-responsive .form-control[type="tel"] {
    min-width: 0;
    width: 100%;
  }

  /* Reduzir o espaço entre os elementos do formulário */
  .form-responsive .form-group,
  .form-responsive .mb-3 {
    margin-bottom: 0.35rem !important;
  }
}

/* ===== BOTÕES ===== */
.btn {
  /* padding: 0.2rem 0.6rem; */
  font-size: var(--lumi-font-size-base);
}

.btn-sm {
  /* padding: 0.1rem 0.4rem; */
  font-size: var(--lumi-font-size-sm);
}

/* ===== CARDS E CONTAINERS ===== */
.card-body {
  padding: var(--lumi-spacing-lg);
}

.card-header, .card-footer {
  padding: var(--lumi-spacing-md) var(--lumi-spacing-lg);
}

/* ===== NAVEGAÇÃO ===== */
/* Ajustes gerais para navegação */
.nav-link {
  padding: 0.3rem 0.5rem;
  font-size: var(--lumi-font-size-base);
}

/* Manter a navegação em Paciente.vue intacta */
.menu-2x2 .nav-link {
  /* Preservar o estilo original */
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* ===== TABELAS ===== */
.table th, .table td {
  padding: 0.3rem 0.4rem;
  font-size: var(--lumi-font-size-base);
}

/* ===== MODAIS ===== */
.modal-header, .modal-footer {
  padding: var(--lumi-spacing-lg);
}

.modal-body {
  padding: var(--lumi-spacing-lg);
}

/* ===== ALERTAS E NOTIFICAÇÕES ===== */
.alert {
  padding: var(--lumi-spacing-md) var(--lumi-spacing-lg);
  font-size: var(--lumi-font-size-base);
}

/* ===== GRID E RESPONSIVIDADE ===== */
/* Classe para aplicar em rows que precisam de inputs lado a lado em mobile */
.row-compact {
  margin-left: -0.2rem;
  margin-right: -0.2rem;
}

.row-compact > [class*="col-"] {
  padding-left: 0.2rem;
  padding-right: 0.2rem;
}

/* Melhorar responsividade em dispositivos móveis */
@media (max-width: 767.98px) {
  /* Permitir que mais colunas caibam lado a lado em mobile */
  .mobile-compact-cols > .col-12 {
    width: auto;
    flex: 1 0 0%;
    max-width: 50%;
  }

  /* Reduzir ainda mais o padding em mobile */
  .mobile-compact-form .form-control,
  .mobile-compact-form .form-select,
  .mobile-compact-form .input-group-text {
    padding: 0.15rem 0.3rem;
    font-size: 0.8rem;
  }

  /* Ajustar tamanho de texto em mobile */
  .mobile-compact-text {
    font-size: 0.8rem;
  }

  /* Ajuste para formulários em mobile - permitir mais inputs por linha */
  .mobile-multi-col .col-12 {
    width: 50%;
    padding-left: 0.2rem;
    padding-right: 0.2rem;
  }

  /* Ajuste para inputs pequenos em mobile (como data, número, etc) */
  .mobile-small-input {
    width: 33.33% !important;
  }

  /* Ajuste para inputs médios em mobile */
  .mobile-medium-input {
    width: 50% !important;
  }

  /* Ajuste para inputs grandes em mobile */
  .mobile-large-input {
    width: 100% !important;
  }
}

/* ===== UTILITÁRIOS ===== */
/* Margens e paddings compactos */
.mt-compact { margin-top: var(--lumi-spacing-sm) !important; }
.mb-compact { margin-bottom: var(--lumi-spacing-sm) !important; }
.mx-compact { margin-left: var(--lumi-spacing-sm) !important; margin-right: var(--lumi-spacing-sm) !important; }
.my-compact { margin-top: var(--lumi-spacing-sm) !important; margin-bottom: var(--lumi-spacing-sm) !important; }

.pt-compact { padding-top: var(--lumi-spacing-sm) !important; }
.pb-compact { padding-bottom: var(--lumi-spacing-sm) !important; }
.px-compact { padding-left: var(--lumi-spacing-sm) !important; padding-right: var(--lumi-spacing-sm) !important; }
.py-compact { padding-top: var(--lumi-spacing-sm) !important; padding-bottom: var(--lumi-spacing-sm) !important; }

/* Classe para aplicar em containers de formulários */
.form-compact .form-group {
  margin-bottom: var(--lumi-spacing-sm);
}

/* Classe para formulários com múltiplos inputs por linha */
.form-multi-column .row > [class*="col-"] {
  padding-left: 0.2rem;
  padding-right: 0.2rem;
}

/* Tabelas compactas */
.table-compact th,
.table-compact td {
  padding: 0.25rem 0.35rem;
  font-size: 0.8rem;
}

/* Cards compactos */
.card-compact .card-body {
  padding: 0.5rem;
}

.card-compact .card-header,
.card-compact .card-footer {
  padding: 0.35rem 0.5rem;
}

/* Fontes compactas */
.fs-compact-sm { font-size: 0.75rem !important; }
.fs-compact-md { font-size: 0.85rem !important; }
.fs-compact-lg { font-size: 0.95rem !important; }

/* ===== COMPONENTES ESPECÍFICOS ===== */
/* Estilos gerais para progress bars */

/* Estilos para o header do Tratamento.vue */
.tratamento-header-progress {
  position: relative;
  width: 100%;
}

.tratamento-header-progress .progress {
  width: 100%;
  height: var(--lumi-input-height);
  margin: 0;
  overflow: hidden;
  background-color: #f8f9fa;
  border: 1px solid #CCC;
  border-radius: 0.375rem;
}

.tratamento-header-progress .progress-bar {
  height: 100%;
  border-radius: 0.375rem;
  background-image: linear-gradient(to right, #4caf50, #45a049);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tratamento-header-progress .progress-value {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.7);
  font-weight: 500 !important;
  font-size: 0.75rem;
  padding: 0px 8px;
  border-radius: 0.375rem;
  color: #444;
  z-index: 2;
  white-space: nowrap;
}

.tratamento-header-progress .progress-info {
  font-size: 0.7rem;
  margin-top: 4px !important;
  color: #666;
}

/* Estilos para o texto-sm abaixo da barra de progresso */
.text-sm {
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
  display: inline-block;
}

/* Status badge padronizado */
.status-badge {
  height: 38px; /* Altura padronizada para combinar com os inputs de data */
  width: 100%;
  padding-top: 9px;
  font-size: 0.9rem;
  font-weight: 600;
}

/* Estilos para a seção de detalhes do paciente em Paciente.vue */
.patient-details-header {
  padding: 0.6rem !important; /* Reduzido o padding */
}

.patient-details-header h5 {
  font-size: 1rem !important; /* Reduzido o tamanho da fonte */
}

.patient-info-item {
  padding: 8px 12px !important; /* Reduzido o padding */
  min-height: 0 !important; /* Removido o min-height */
}

.patient-info-item .info-icon {
  width: 28px !important; /* Reduzido o tamanho do ícone */
  height: 28px !important;
  margin-right: 8px !important;
}

.patient-info-item .info-content {
  font-size: 0.85rem !important; /* Reduzido o tamanho da fonte */
  line-height: 1.3 !important;
}

/* Ajustes para a seção de detalhes em Analise e Diagnostico */
.custom-card-header {
  padding: 8px 12px !important;
  font-size: 0.95rem !important;
}

/* ===== GRID RESPONSIVO OTIMIZADO ===== */
/* Classes para criar layouts mais compactos com mais colunas */

/* Grid de 3 colunas em mobile */
.row-cols-mobile-3 > * {
  flex: 0 0 auto;
  width: 33.33333%;
}

/* Grid de 4 colunas em mobile */
.row-cols-mobile-4 > * {
  flex: 0 0 auto;
  width: 25%;
}

/* Ajuste para colunas em telas pequenas */
@media (max-width: 767.98px) {
  .col-sm-compact-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-sm-compact-4 {
    flex: 0 0 auto;
    width: 33.33333%;
  }

  .col-sm-compact-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  /* Reduzir o gap entre colunas */
  .row-compact-gap {
    margin-left: -0.15rem;
    margin-right: -0.15rem;
  }

  .row-compact-gap > [class*="col-"] {
    padding-left: 0.15rem;
    padding-right: 0.15rem;
  }
}
