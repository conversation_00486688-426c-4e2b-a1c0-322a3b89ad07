import axios from '@/services/axios'

export async function getConsultas() {
    try {
        const response = await axios.get('/consultas');

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consultas:', error);
    }

    return false;
}

export async function getConsultasByPaciente(paciente_id) {
    try {
        const response = await axios.get(`/consultas/paciente/${paciente_id}`);

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consultas do paciente:', error);
    }

    return false;
}

export async function getConsultasByDentista(dentista_id) {
    try {
        const response = await axios.get(`/consultas/dentista/${dentista_id}`);

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consultas do dentista:', error);
    }

    return false;
}

export async function getConsultasByData(data) {
    try {
        const response = await axios.get(`/consultas/data/${data}`);

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consultas por data:', error);
    }

    return false;
}

export async function getConsulta(id) {
    try {
        console.log('Buscando consulta com ID:', id);
        const response = await axios.get(`/consultas/${id}`);

        if (!response) {
            console.error('Resposta vazia ao buscar consulta');
            return false;
        }

        if (!response.data) {
            console.error('Dados vazios ao buscar consulta');
            return false;
        }

        // Log detalhado para entender a estrutura exata da resposta
        console.log('Resposta completa da API:', response);
        console.log('Dados da consulta recebidos (response.data):', response.data);
        console.log('Tipo de response.data:', typeof response.data);

        if (typeof response.data === 'object') {
            console.log('Chaves do objeto response.data:', Object.keys(response.data));

            // Verificar se a resposta está no formato de API Resource do Laravel
            if (response.data.data) {
                console.log('Dados encontrados dentro do objeto data:', response.data.data);
                console.log('Tipo de response.data.data:', typeof response.data.data);

                if (typeof response.data.data === 'object') {
                    console.log('Chaves do objeto response.data.data:', Object.keys(response.data.data));
                }

                return response.data.data;
            }
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consulta:', error);
    }

    return false;
}

export async function novaConsulta(consulta) {
    try {
        console.log('Criando nova consulta:', consulta);

        // Se a consulta tem data e horário separados, vamos combiná-los
        if (consulta.data && consulta.horario) {
            // O backend espera um timestamp MySQL completo (YYYY-MM-DD HH:MM:SS)
            // Vamos combinar a data e o horário
            consulta.horario = `${consulta.data} ${consulta.horario}:00`;
            console.log('Horário combinado:', consulta.horario);

            // Remove o campo data que não é esperado pelo backend
            delete consulta.data;
        }

        const response = await axios.post('/consultas', consulta);
        console.log('Resposta ao criar consulta:', response.data);

        if (!response || !response.data) {
            console.error('Resposta vazia ao criar consulta');
            return false;
        }

        if (response.data.status === 'error') {
            console.error('Erro retornado pelo servidor:', response.data);
            return false;
        }

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao criar nova consulta:', error);
    }

    return false;
}

export async function atualizarConsulta(id, consulta) {
    try {
        console.log('Atualizando consulta ID:', id, 'Dados:', consulta);

        // Se a consulta tem data e horário separados, vamos combiná-los
        if (consulta.data && consulta.horario) {
            // O backend espera um timestamp MySQL completo (YYYY-MM-DD HH:MM:SS)
            // Vamos combinar a data e o horário
            consulta.horario = `${consulta.data} ${consulta.horario}:00`;
            console.log('Horário combinado:', consulta.horario);

            // Remove o campo data que não é esperado pelo backend
            delete consulta.data;
        }

        const response = await axios.put(`/consultas/${id}`, consulta);
        console.log('Resposta ao atualizar consulta:', response.data);

        if (!response || !response.data) {
            console.error('Resposta vazia ao atualizar consulta');
            return false;
        }

        if (response.data.status === 'error') {
            console.error('Erro retornado pelo servidor:', response.data);
            return false;
        }

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao atualizar consulta:', error);
    }

    return false;
}

export async function atualizarStatusConsulta(id, status) {
    try {
        console.log('Atualizando status da consulta ID:', id, 'Status:', status);

        const response = await axios.patch(`/consultas/${id}/status`, { status });
        console.log('Resposta ao atualizar status:', response.data);

        if (!response || !response.data) {
            console.error('Resposta vazia ao atualizar status');
            return false;
        }

        if (response.data.status === 'error') {
            console.error('Erro retornado pelo servidor:', response.data);
            return false;
        }

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao atualizar status da consulta:', error);
    }

    return false;
}

export async function excluirConsulta(id) {
    try {
        console.log('Excluindo consulta ID:', id);

        const response = await axios.delete(`/consultas/${id}`);
        console.log('Resposta ao excluir consulta:', response.data);

        if (!response || !response.data) {
            console.error('Resposta vazia ao excluir consulta');
            return false;
        }

        if (response.data.status === 'error') {
            console.error('Erro retornado pelo servidor:', response.data);
            return false;
        }

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao excluir consulta:', error);
    }

    return false;
}