import moment from 'moment'
import * as utils from './utils.js'

// Set locale globally once
moment.locale('pt-br')

const filters = {
    howMuchTime(compareDate, options = {}) {

        if (!compareDate)
            return '-';

        const defaultOptions = {
            type: 'datetime',
            compareTo: new Date(),
            prefix: true,
        }

        options = {
            ...defaultOptions,
            ...options
        }

        let { compareTo, type, prefix } = options

        if (typeof compareDate == 'string') {
            if (type == 'date') {
                // Check if compareDate already has a time component and remove it
                if (compareDate.includes('T') || compareDate.includes(' ')) {
                    // Extract just the date part (YYYY-MM-DD)
                    compareDate = compareDate.split('T')[0].split(' ')[0];
                }
                compareDate = new Date(compareDate + 'T00:00:00')
            }
            else if (type == 'datetime')
                compareDate = new Date(compareDate)
        }

        if (typeof compareTo == 'string')
            compareTo = new Date(compareTo)

        const interval = timeInterval(compareDate, compareTo)
        const diferencaEmMilissegundos = compareDate - compareTo
        const { years: anos, months: meses, weeks: semanas, days, hours, minutes, seconds, totalDays } = interval
        const futuro = (diferencaEmMilissegundos >= 0)

        if (type == 'date' && Math.abs(totalDays) < 1)
            return 'hoje'

        let resposta = []

        if (prefix) {
            if (futuro)
                resposta.push('em') // Futuro
            else
                resposta.push('há') // Passado
        }

        // Regra 1: Se tiver anos, mostra anos e meses
        if (anos > 1) {
            resposta.push(`${anos} anos`)
            if (meses > 0) {
                resposta.push('e')
                if (meses > 1)
                    resposta.push(`${meses} meses`)
                else
                    resposta.push(`${meses} mês`)
            }
            return resposta.join(' ')
        }
        else if (anos === 1) {
            resposta.push(`${anos} ano`)
            if (meses > 0) {
                resposta.push('e')
                if (meses > 1)
                    resposta.push(`${meses} meses`)
                else
                    resposta.push(`${meses} mês`)
            }
            return resposta.join(' ')
        }

        // Regra 2: Se tiver meses (mas não anos), mostra apenas meses
        // Exceto se for 1 mês, aí mostra também as semanas
        if (meses > 1) {
            resposta.push(`${meses} meses`)
            return resposta.join(' ')
        }
        else if (meses === 1) {
            resposta.push(`${meses} mês`)
            if (semanas > 0) {
                resposta.push('e')
                if (semanas > 1)
                    resposta.push(`${semanas} semanas`)
                else
                    resposta.push(`${semanas} semana`)
            }
            return resposta.join(' ')
        }

        // Regra 3: Se tiver semanas (mas não meses nem anos), mostra apenas semanas
        if (semanas > 1) {
            resposta.push(`${semanas} semanas`)
            return resposta.join(' ')
        }
        else if (semanas === 1) {
            resposta.push(`${semanas} semana`)
            return resposta.join(' ')
        }

        // Regra 4: Se tiver apenas dias
        if (days > 0) {
            // Caso especial para "amanhã" e "ontem"
            if (Math.floor(totalDays) === 1) {
                if (futuro)
                    return 'amanhã'
                else
                    return 'ontem'
            }

            // Para outros dias
            const roundDays = Math.floor(days)
            if (roundDays > 0) {
                resposta.push(`${roundDays} dia${roundDays > 1 ? 's' : ''}`)
                return resposta.join(' ')
            }
        }

        // Regra 5: Se for menos de um dia e o tipo for 'datetime'
        if (type === 'datetime') {
            if (hours > 0) {
                resposta.push(hours + ' hora' + (hours > 1 ? 's' : ''));
            } else if (minutes > 0) {
                resposta.push(minutes + ' minuto' + (minutes > 1 ? 's' : ''));
            } else {
                resposta.push('poucos segundos');
            }
        }

        return resposta.join(' ')
    },
    dateDmy(value) {
        if (!value)
            return '-'
        return moment(String(value)).format('DD/MM/YYYY')
    },
    dateYmd(value) {
        if (!value)
            return '-'
        return moment(String(value)).format('YYYY-MM-DD')
    },
    dateDDY(value) {
        if (!value)
            return '-'
        return utils.capitalizeFirst(moment(String(value)).format('MMMM/YYYY'))
    },
    dateTime(value, beautify = true) {
        if (!value)
            return '-'

        let format

        if (beautify)
            format = 'DD/MM/YYYY, à\\s HH:mm\\h'
        else
            format = 'DD/MM/YYYY HH:mm'

        return moment(String(value)).format(format)
    },
    dateLong(value) {
        if (!value)
            return '-'
        return moment(String(value)).format('dddd, DD [de] MMMM [de] YYYY')
    },
    date(value) {
        if (!value)
            return '-'
        return moment(String(value)).format('DD/MM/YYYY')
    },
    time(value) {
        if (!value)
            return '-'
        return moment(String(value)).format('HH:mm')
    },
}

function timeInterval(date1, date2) {
    if (date1 > date2) { // swap
        return timeInterval(date2, date1);
    }

    // Criar cópias das datas para não modificar as originais
    const d1 = new Date(date1.getTime());
    const d2 = new Date(date2.getTime());

    // Resetar horas para comparar apenas as datas
    d1.setHours(0, 0, 0, 0);
    d2.setHours(0, 0, 0, 0);

    // Calcular a diferença em milissegundos
    const diffMs = d2.getTime() - d1.getTime();

    // Calcular a diferença em dias
    const diffDays = diffMs / (1000 * 3600 * 24);

    // Calcular anos e meses de forma mais precisa
    let years = d2.getFullYear() - d1.getFullYear();
    let months = d2.getMonth() - d1.getMonth();

    // Ajustar meses e anos se necessário
    if (d2.getDate() < d1.getDate()) {
        months--;
    }

    if (months < 0) {
        years--;
        months += 12;
    }

    // Calcular dias restantes após subtrair anos e meses
    let remainingDays = diffDays;

    // Subtrair dias dos anos
    if (years > 0) {
        const yearDays = years * 365 + Math.floor(years / 4); // Aproximação para anos bissextos
        remainingDays -= yearDays;
    }

    // Subtrair dias dos meses
    if (months > 0) {
        // Dias aproximados por mês
        const monthDays = months * 30.44; // Média de dias por mês (365.25 / 12)
        remainingDays -= monthDays;
    }

    // Calcular semanas e dias restantes
    const weeks = Math.floor(remainingDays / 7);
    const days = Math.floor(remainingDays % 7);

    // Calcular horas, minutos e segundos
    const totalHours = diffDays * 24;
    const hours = Math.floor(totalHours % 24);
    const minutes = Math.floor((totalHours * 60) % 60);
    const seconds = Math.floor((totalHours * 3600) % 60);

    return {
        years,
        months,
        weeks,
        days,
        hours,
        minutes,
        seconds,
        totalDays: diffDays
    };
}

export default filters