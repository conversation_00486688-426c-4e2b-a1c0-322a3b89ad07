@tailwind base;
@tailwind components;
@tailwind utilities;
@tailwind variants;

* {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}


@media screen and (-webkit-min-device-pixel-ratio:0)/*chrome*/
{
  .custom-scrll::-webkit-scrollbar
  {
   width:6px;
   height:6px;
  }
  .custom-scrll::-webkit-scrollbar-track
  {
  background:transparent;
  }
  .custom-scrll::-webkit-scrollbar-thumb
  {
    border-radius:6px;
    background: rgba(14, 165, 233, .8);
    border:0;
    display: none;
  }
  .custom-scrll:hover
  {
    &.custom-scrll::-webkit-scrollbar-thumb{
      display: initial;
    }
  }
  .custom-scrll::-webkit-scrollbar-corner
  {
    display:none;
  }
}

@-moz-document url-prefix()/*firefox*/
{
  .custom-scrll
  {
    scrollbar-color: rgba(14, 165, 233, .8) transparent;
    scrollbar-width: none;
    &:hover{
    scrollbar-width: thin;
    }
  }
}