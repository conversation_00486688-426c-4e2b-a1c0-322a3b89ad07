<?php

namespace App\Http\Controllers;

use App\Models\NecessidadeEncaminhamento;
use App\Models\Paciente;
use Illuminate\Http\Request;

class NecessidadeEncaminhamentoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->payload();

        $query = NecessidadeEncaminhamento::with('paciente');

        // Se não for admin do sistema, filtra apenas os encaminhamentos dos pacientes da clínica do usuário
        if (!$user['system_admin']) {
            $query->whereHas('paciente', function ($q) use ($user) {
                $q->where('clinica_id', $user['clinica']['id']);
            });
        }

        $necessidadesEncaminhamentos = $query->get();

        return response()->json($necessidadesEncaminhamentos);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $user = auth()->payload();
            $body = $request->all();

            // Validação dos dados
            $request->validate([
                'paciente_id' => 'required|exists:pacientes,id',
                'especialista' => 'required|string',
                'necessidade' => 'required|string',
                'status' => 'sometimes|string',
                'observacoes' => 'sometimes|nullable|string',
            ]);

            // Verifica se o usuário tem permissão para criar um encaminhamento para este paciente
            if (!$user['system_admin']) {
                $paciente = Paciente::find($body['paciente_id']);

                if (!$paciente || $paciente->clinica_id !== $user['clinica']['id']) {
                    return responseError([
                        'message' => 'Você não tem permissão para criar um encaminhamento para este paciente.'
                    ]);
                }
            }

            // Cria o encaminhamento
            $necessidadeEncaminhamento = new NecessidadeEncaminhamento();
            $necessidadeEncaminhamento->paciente_id = $body['paciente_id'];
            $necessidadeEncaminhamento->especialista = $body['especialista'];
            $necessidadeEncaminhamento->necessidade = $body['necessidade'];
            $necessidadeEncaminhamento->status = $body['status'] ?? 'PENDENTE';
            $necessidadeEncaminhamento->observacoes = $body['observacoes'] ?? null;
            $necessidadeEncaminhamento->save();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = auth()->payload();

        $necessidadeEncaminhamento = NecessidadeEncaminhamento::with('paciente')->find($id);

        if (!$necessidadeEncaminhamento) {
            return response()->json(['error' => 'Necessidade de encaminhamento não encontrada.'], 404);
        }

        // Verifica se o usuário tem permissão para visualizar este encaminhamento
        if (!$user['system_admin'] && $necessidadeEncaminhamento->paciente->clinica_id !== $user['clinica']['id']) {
            return response()->json(['error' => 'Você não tem permissão para visualizar este encaminhamento.'], 403);
        }

        return response()->json($necessidadeEncaminhamento);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $user = auth()->payload();
            $body = $request->all();

            $necessidadeEncaminhamento = NecessidadeEncaminhamento::with('paciente')->find($id);

            if (!$necessidadeEncaminhamento) {
                return response()->json(['error' => 'Necessidade de encaminhamento não encontrada.'], 404);
            }

            // Verifica se o usuário tem permissão para atualizar este encaminhamento
            if (!$user['system_admin'] && $necessidadeEncaminhamento->paciente->clinica_id !== $user['clinica']['id']) {
                return response()->json(['error' => 'Você não tem permissão para atualizar este encaminhamento.'], 403);
            }

            // Atualiza os campos
            $necessidadeEncaminhamento->fill($body);
            $necessidadeEncaminhamento->save();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $user = auth()->payload();

            $necessidadeEncaminhamento = NecessidadeEncaminhamento::with('paciente')->find($id);

            if (!$necessidadeEncaminhamento) {
                return response()->json(['error' => 'Necessidade de encaminhamento não encontrada.'], 404);
            }

            // Verifica se o usuário tem permissão para excluir este encaminhamento
            if (!$user['system_admin'] && $necessidadeEncaminhamento->paciente->clinica_id !== $user['clinica']['id']) {
                return response()->json(['error' => 'Você não tem permissão para excluir este encaminhamento.'], 403);
            }

            $necessidadeEncaminhamento->delete();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }
    }
}
