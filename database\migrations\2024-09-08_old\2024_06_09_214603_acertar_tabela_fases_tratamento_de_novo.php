<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fases_tratamento', function (Blueprint $table) {
            $table->dropColumn('descricao');
            $table->string('nome');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fases_tratamento', function (Blueprint $table) {
            $table->string('descricao');
            $table->dropColumn('nome');
        });
    }
};
