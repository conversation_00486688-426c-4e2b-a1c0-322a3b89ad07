<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\ActionHistory;
use App\Models\User;
use App\Models\Clinica;
use App\Services\ActionHistoryService;

class TestActionHistory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'actionhistory:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the ActionHistory system by creating a sample entry';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing ActionHistory system...');

        try {
            // Check if we have users and clinicas
            $user = User::first();
            $clinica = Clinica::first();

            if (!$user) {
                $this->error('No users found in database. Please create a user first.');
                return 1;
            }

            if (!$clinica) {
                $this->error('No clinicas found in database. Please create a clinica first.');
                return 1;
            }

            // Create a test ActionHistory entry
            $actionHistory = ActionHistory::create([
                'user_id' => $user->id,
                'action_type' => ActionHistory::ACTION_CREATE,
                'action_description' => 'Test action history entry created by command',
                'http_method' => 'COMMAND',
                'endpoint' => 'artisan:actionhistory:test',
                'clinica_id' => $clinica->id,
                'entity_type' => 'Test',
                'entity_id' => 999,
                'new_data' => [
                    'test_field' => 'test_value',
                    'created_by' => 'artisan_command',
                    'timestamp' => now()->toISOString(),
                ],
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Laravel Artisan Command',
            ]);

            $this->info("✅ ActionHistory entry created successfully!");
            $this->info("   ID: {$actionHistory->id}");
            $this->info("   User: {$user->name} (ID: {$user->id})");
            $this->info("   Clinica: {$clinica->nome} (ID: {$clinica->id})");
            $this->info("   Action: {$actionHistory->action_type}");
            $this->info("   Description: {$actionHistory->action_description}");

            // Test the service
            $this->info("\nTesting ActionHistoryService...");
            $service = app(ActionHistoryService::class);
            $this->info("✅ ActionHistoryService instantiated successfully!");

            // Test data sanitization
            $testData = [
                'normal_field' => 'normal_value',
                'password' => 'should_be_removed',
                'api_token' => 'should_be_removed',
            ];

            $reflection = new \ReflectionClass($service);
            $method = $reflection->getMethod('sanitizeData');
            $method->setAccessible(true);
            $sanitized = $method->invoke($service, $testData);

            if (!isset($sanitized['password']) && !isset($sanitized['api_token']) && isset($sanitized['normal_field'])) {
                $this->info("✅ Data sanitization working correctly!");
            } else {
                $this->error("❌ Data sanitization not working properly!");
            }

            // Check database table structure
            $this->info("\nChecking database table structure...");
            $columns = DB::select("DESCRIBE action_histories");
            $expectedColumns = [
                'id', 'user_id', 'action_type', 'action_description', 'http_method', 'endpoint',
                'paciente_id', 'dentista_id', 'clinica_id', 'entity_type', 'entity_id',
                'old_data', 'new_data', 'ip_address', 'user_agent', 'created_at', 'updated_at'
            ];

            $actualColumns = array_column($columns, 'Field');
            $missingColumns = array_diff($expectedColumns, $actualColumns);

            if (empty($missingColumns)) {
                $this->info("✅ Database table structure is correct!");
            } else {
                $this->error("❌ Missing columns: " . implode(', ', $missingColumns));
            }

            // Test relationships
            $this->info("\nTesting model relationships...");
            $actionHistory->load(['user', 'clinica']);

            if ($actionHistory->user && $actionHistory->clinica) {
                $this->info("✅ Model relationships working correctly!");
                $this->info("   User relationship: {$actionHistory->user->name}");
                $this->info("   Clinica relationship: {$actionHistory->clinica->nome}");
            } else {
                $this->error("❌ Model relationships not working properly!");
            }

            // Test scopes
            $this->info("\nTesting query scopes...");
            $createActions = ActionHistory::byActionType(ActionHistory::ACTION_CREATE)->count();
            $userActions = ActionHistory::byUser($user->id)->count();
            $clinicaActions = ActionHistory::byClinica($clinica->id)->count();

            $this->info("✅ Query scopes working correctly!");
            $this->info("   CREATE actions: {$createActions}");
            $this->info("   Actions by user {$user->id}: {$userActions}");
            $this->info("   Actions by clinica {$clinica->id}: {$clinicaActions}");

            $this->info("\n🎉 ActionHistory system test completed successfully!");
            $this->info("The system is ready for production use.");

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ ActionHistory test failed: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }
}
