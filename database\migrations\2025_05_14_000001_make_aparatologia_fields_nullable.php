<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aparatologia', function (Blueprint $table) {
            $table->text('aparelho_utilizado')->nullable()->change();
            $table->text('tipo_colagem')->nullable()->change();
            $table->text('contencao_superior')->nullable()->change();
            $table->text('contencao_inferior')->nullable()->change();
            $table->text('exercicios_miofuncionais')->nullable()->change();
            $table->text('observacoes')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aparatologia', function (Blueprint $table) {
            $table->text('aparelho_utilizado')->nullable(false)->change();
            $table->text('tipo_colagem')->nullable(false)->change();
            $table->text('contencao_superior')->nullable(false)->change();
            $table->text('contencao_inferior')->nullable(false)->change();
            $table->text('exercicios_miofuncionais')->nullable(false)->change();
            $table->text('observacoes')->nullable(false)->change();
        });
    }
};
