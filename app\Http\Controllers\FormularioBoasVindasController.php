<?php

namespace App\Http\Controllers;

use App\Models\FormularioBoasVindas;
use Illuminate\Http\Request;

class FormularioBoasVindasController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $body = $request->all();

        $form = new FormularioBoasVindas();
        $form->horario = date('Y-m-d H:i:s');
        $form->nome = $body['nome'];
        $form->email = $body['email'];
        $form->whatsapp = $body['whatsapp'];
        $form->dados = $body['dados'];
        $form->save();

        response()->json(['status' => 'success']);
    }

    /**
     * Display the specified resource.
     */
    public function show(FormularioBoasVindas $formularioBoasVindas)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FormularioBoasVindas $formularioBoasVindas)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, FormularioBoasVindas $formularioBoasVindas)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FormularioBoasVindas $formularioBoasVindas)
    {
        //
    }
}
