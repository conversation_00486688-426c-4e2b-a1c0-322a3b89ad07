<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEnderecoFieldsToDentistasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('dentistas', function (Blueprint $table) {
            $table->string('endereco_cep')->nullable()->after('nome');
            $table->string('endereco_logradouro')->nullable()->after('endereco_cep');
            $table->string('endereco_numero')->nullable()->after('endereco_logradouro');
            $table->string('endereco_complemento')->nullable()->after('endereco_numero');
            $table->string('endereco_cidade')->nullable()->after('endereco_complemento');
            $table->string('endereco_estado')->nullable()->after('endereco_cidade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('dentistas', function (Blueprint $table) {
            $table->dropColumn('endereco_estado');
            $table->dropColumn('endereco_cidade');
            $table->dropColumn('endereco_complemento');
            $table->dropColumn('endereco_numero');
            $table->dropColumn('endereco_logradouro');
            $table->dropColumn('endereco_cep');
        });
    }
}
