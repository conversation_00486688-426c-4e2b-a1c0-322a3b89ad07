<template>
  <div>
    <div class="mt-2 contacts-container" style="font-size: 12pt">
      <div class="contatos-flex-container">
        <div class="contatos-flex-header">
          <div class="contatos-flex-icon-cell"></div>
          <div class="contatos-flex-content-cell">
            <label class="form-control-label">Contato</label>
          </div>
          <div v-if="!isEditing" class="contatos-flex-content-cell second-cell">
            <label class="form-control-label">Descrição</label>
          </div>
          <div v-if="isEditing" class="contatos-flex-action-cell"></div>
        </div>
        <div
          class="contatos-flex-row"
          v-for="contato in paciente.contatos"
          v-bind:key="contato.id"
        >
          <div class="contatos-flex-icon-cell">
            <font-awesome-icon
              v-if="contato.tipo != 'telefone'"
              :icon="getContatoIcon(contato.tipo)"
              :class="{
                'text-success': contato.tipo == 'whatsapp',
                'fs-14': contato.tipo == 'email',
                'fs-15': contato.tipo != 'email',
              }"
            />
            <v-icon
              v-if="contato.tipo == 'telefone'"
              style="font-size: 17pt"
              >{{ getContatoIcon(contato.tipo) }}</v-icon
            >
          </div>
          <div class="contatos-flex-content-cell" data-label="Contato:">
            <a
              :href="getContatoHref(contato.tipo, contato.contato)"
              class="hoverable"
              target="_blank"
            >
              {{ contato.contato }}
            </a>
          </div>
          <div v-if="!isEditing" class="contatos-flex-content-cell second-cell" data-label="Descrição:">
            {{ contato.descricao }}
          </div>
          <div v-if="isEditing"
          class="contatos-flex-action-cell">
            <button
              class="btn btn-vsm btn-sm btn-danger"
              @click="excluirContato(contato.id, contato.tipo)"
            >
              <font-awesome-icon :icon="['fas', 'trash']" />
            </button>
          </div>
        </div>
      </div>
      <div class="row align-items-center">
        <div class="col-12 align-items-center">
          <div class="input-group input-group-sm novo-contato-row">
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle contact-type-btn"
                type="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
                @click="toggleDropdown"
                style="border-top-left-radius: 0px; height: 34px;"
              >
                <font-awesome-icon
                  v-if="localNovoContato.tipo == 'email'"
                  :icon="['fas', 'envelope']"
                  style="font-size: 15pt"
                />
                <v-icon
                  v-if="localNovoContato.tipo == 'telefone'"
                  style="font-size: 17pt"
                  >mdi-phone</v-icon
                >
                <font-awesome-icon
                  v-if="localNovoContato.tipo == 'celular'"
                  :icon="['fas', 'mobile-screen-button']"
                  style="font-size: 15pt"
                />
                <font-awesome-icon
                  v-if="localNovoContato.tipo == 'whatsapp'"
                  :icon="['fab', 'whatsapp']"
                  class="text-success"
                  style="font-size: 16pt"
                />
              </button>
              <ul class="dropdown-menu contact-dropdown-menu" :class="{ show: dropdownOpen }">
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    @click.prevent="handleSelectMeioContato('email')"
                    title="E-mail"
                  >
                    <font-awesome-icon
                      :icon="['fas', 'envelope']"
                      style="font-size: 14pt; margin-right: 8px"
                    />
                    E-mail
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    @click.prevent="handleSelectMeioContato('telefone')"
                    title="Telefone"
                  >
                    <v-icon style="font-size: 17pt; margin-right: 8px">mdi-phone</v-icon>
                    Telefone
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    @click.prevent="handleSelectMeioContato('celular')"
                    title="Celular"
                  >
                    <font-awesome-icon
                      :icon="['fas', 'mobile-screen-button']"
                      style="font-size: 15pt; margin-right: 8px"
                    />
                    Celular
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    @click.prevent="handleSelectMeioContato('whatsapp')"
                    title="WhatsApp"
                  >
                    <font-awesome-icon
                      :icon="['fab', 'whatsapp']"
                      class="text-success"
                      style="font-size: 16pt; margin-right: 8px"
                    />
                    WhatsApp
                  </a>
                </li>
              </ul>
            </div>
            <input type="text" class="form-control"
              :placeholder="getContatoPlaceholder"
              v-model="localNovoContato.contato"
              ref="contatoInput"
              v-maska="novoContatoMask"
              @input="handleContatoChange">
            <input type="text" placeholder="Descrição"
              v-model="localNovoContato.descricao" class="form-control"
              ref="contatoDescricaoInput">

            <button
              class="btn btn-primary m-0 p-0"
              @click="adicionarContato"
              style="font-size: 1rem; width: 62px; min-width: 62px; height: 34px;"
            >
              <font-awesome-icon :icon="['fas', 'plus']" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

.contatos-flex-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 1px solid #DDD;
  border-radius: 0.375rem 0.375rem 0 0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.contatos-flex-header, .contatos-flex-row {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;
  border-bottom: 1px solid #DDD;
}

.contatos-flex-row:last-child {
  border-bottom: none;
}

.contatos-flex-row {
  transition: background-color 0.15s ease;
}

.contatos-flex-row:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.contatos-flex-icon-cell {
  width: 59px;
  min-width: 59px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F3F3F3;
  padding: 4px 5px;
  border-right: 1px solid #DDD;
}

/* Remove background from icon cell in header */
.contatos-flex-header .contatos-flex-icon-cell {
  background: transparent;
  padding: 2px 5px;
}

.contatos-flex-content-cell {
  flex: 1;
  padding: 4px 10px;
  border-right: 1px solid #DDD;
  display: flex;
  align-items: center;
}

.contatos-flex-content-cell.second-cell {
  flex: 1 82px; /* This makes it take up its share plus 72px extra */
}

@media (max-width: 576px) {
  .contatos-flex-content-cell.second-cell {
    flex: 1 56px; /* This makes it take up its share plus 56px extra */
  }
}

.contatos-flex-action-cell {
  width: 62px;
  min-width: 62px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 5px;
}

.contatos-flex-header {
  font-weight: 600;
  background-color: #f8f9fa;
  border-bottom: 1px solid #DDD;
  letter-spacing: 0.3px;
  color: #495057;
  font-size: 0.9rem;
}

.contatos-flex-header .contatos-flex-content-cell {
  padding: 2px 10px;
}

/* Remove border between cells in header */
.contatos-flex-header .contatos-flex-icon-cell {
  border-right: none;
}

.contatos-flex-header .contatos-flex-content-cell:first-of-type {
  border-right: none;
}

@media (max-width: 576px) {
  .contatos-flex-icon-cell {
    width: 49px;
    min-width: 49px;
    padding: 2px 5px;
  }
  .contatos-flex-action-cell {
    width: 52px;
    min-width: 52px;
  }

  /* Adjust button sizes for mobile */
  .novo-contato-row button.dropdown {
    width: 49px !important;
    min-width: 49px !important;
  }

  .novo-contato-row button.btn-primary {
    width: 52px !important;
    min-width: 52px !important;
  }
}

/* Contact Type Dropdown Styles */
.contact-type-btn {
  background: #F3F3F3;
  border: 1px solid #DDD;
  border-radius: 0.375rem 0 0 0.375rem;
  padding: 0.375rem 0.75rem;
  width: 60px;
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease-in-out;
}

.contact-type-btn:hover {
  background: #E9E9E9;
  border-color: #CCC;
}

.contact-type-btn:focus {
  background: #E9E9E9;
  border-color: #6ca5db;
  box-shadow: 0 0 0 0.2rem rgba(108, 165, 219, 0.25);
}

.contact-dropdown-menu {
  min-width: 140px;
  border: 1px solid #CCC;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 0.25rem 0;
  margin-top: 0.125rem;
}

.contact-dropdown-menu .dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #495057;
  display: flex;
  align-items: center;
  transition: all 0.15s ease-in-out;
}

.contact-dropdown-menu .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.contact-dropdown-menu .dropdown-item:active {
  background-color: #e9ecef;
  color: #495057;
}

@media (max-width: 576px) {
  .contact-type-btn {
    width: 50px;
    min-width: 50px;
    padding: 0.375rem 0.5rem;
  }
}

/* Mobile layout for contacts (sm screens and below) */
/* @media (max-width: 576px) {
  .contatos-flex-header, .contatos-flex-row {
    flex-direction: column;
    border-bottom: none;
  }

  .contatos-flex-row {
    margin-bottom: 10px;
    border: 1px solid #DDD;
    border-radius: 0.375rem;
  }

  .contatos-flex-icon-cell {
    width: 100%;
    min-width: 100%;
    border-right: none;
    border-bottom: 1px solid #DDD;
    padding: 5px;
    justify-content: flex-start;
  }

  .contatos-flex-content-cell {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #DDD;
    padding: 5px 10px;
  }

  .contatos-flex-action-cell {
    width: 100%;
    min-width: 100%;
    padding: 5px;
    justify-content: flex-start;
  }

  // Hide header on mobile, as we'll use a different approach
  .contatos-flex-header {
    display: none;
  }

  // Add labels inside cells for mobile
  .contatos-flex-content-cell::before {
    content: attr(data-label);
    font-weight: bold;
    margin-right: 10px;
  }
} */
</style>

<script>

import { phoneMask } from "@/helpers/utils.js";
import { vMaska } from "maska/vue"

export default {
  name: "PatientContactInfo",
  directives: { maska: vMaska },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    isEditing: {
      type: Boolean,
      default: false
    },
    isMobile: {
      type: Boolean,
      default: false
    },
    novoContato: {
      type: Object,
      required: true
    },
    getContatoPlaceholder: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      dropdownOpen: false,
      localNovoContato: {
        tipo: this.novoContato.tipo,
        contato: this.novoContato.contato,
        descricao: this.novoContato.descricao
      }
    };
  },
  watch: {
    novoContato: {
      handler(newVal) {
        // Update local state when parent prop changes
        this.localNovoContato = {
          tipo: newVal.tipo,
          contato: newVal.contato,
          descricao: newVal.descricao
        };
      },
      deep: true,
      immediate: true // Ensure it runs immediately on component creation
    },
    // Watch local changes and emit updates to parent
    localNovoContato: {
      handler(newVal) {
        // Only emit if the values are different to avoid circular updates
        if (newVal.tipo !== this.novoContato.tipo) {
          this.$emit('update:field', { field: 'novoContato.tipo', value: newVal.tipo });
        }
        if (newVal.contato !== this.novoContato.contato) {
          this.$emit('update:field', { field: 'novoContato.contato', value: newVal.contato });
        }
        if (newVal.descricao !== this.novoContato.descricao) {
          this.$emit('update:field', { field: 'novoContato.descricao', value: newVal.descricao });
        }
      },
      deep: true
    }
  },
  mounted() {
    // Add click outside listener to close dropdown
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    // Remove click outside listener
    document.removeEventListener('click', this.handleClickOutside);
  },
  computed: {
    novoContatoMask() {
      return ["telefone", "celular", "whatsapp"].includes(this.novoContato.tipo)
        ? this.phoneMask(this.novoContato.contato)
        : "";
    },
  },
  methods: {
    phoneMask,
    getContatoIcon(tipo) {
      switch (tipo) {
        case 'email':
          return ['fas', 'envelope'];
        case 'telefone':
          return 'mdi-phone';
        case 'celular':
          return ['fas', 'mobile-screen-button'];
        case 'whatsapp':
          return ['fab', 'whatsapp'];
        default:
          return ['fas', 'question'];
      }
    },
    getContatoHref(tipo, contato) {
      switch (tipo) {
        case 'email':
          return `mailto:${contato}`;
        case 'telefone':
        case 'celular':
          return `tel:${contato.replace(/\D/g, '')}`;
        case 'whatsapp':
          return `https://wa.me/55${contato.replace(/\D/g, '')}`;
        default:
          return '#';
      }
    },
    toggleDropdown() {
      this.dropdownOpen = !this.dropdownOpen;
    },
    handleClickOutside(event) {
      // Check if click is outside the dropdown
      const dropdown = this.$el.querySelector('.dropdown');
      if (dropdown && !dropdown.contains(event.target)) {
        this.dropdownOpen = false;
      }
    },
    handleSelectMeioContato(tipo) {
      this.localNovoContato.tipo = tipo;
      this.dropdownOpen = false; // Close dropdown after selection
      this.$emit('select-meio-contato', tipo);
      // Também emitir o evento update:field para manter consistência
      this.$emit('update:field', { field: 'novoContato.tipo', value: tipo });

      // Use nextTick to ensure the DOM has updated before trying to focus
      this.$nextTick(() => {
        if (this.$refs.contatoInput) {
          this.$refs.contatoInput.focus();
        }
      });
    },
    handleContatoChange(event) {
      if (event && event.target) {
        this.localNovoContato.contato = event.target.value;
        this.$emit('contato-change', event);
        // Também emitir o evento update:field para manter consistência
        this.$emit('update:field', { field: 'novoContato.contato', value: this.localNovoContato.contato });

        // Check if we should auto-focus to the description field
        if ((this.localNovoContato.tipo === 'celular' || this.localNovoContato.tipo === 'whatsapp') &&
            this.localNovoContato.contato.length > 14 &&
            this.$refs.contatoDescricaoInput) {
          this.$nextTick(() => {
            this.$refs.contatoDescricaoInput.focus();
          });
        }
      }
    },
    adicionarContato() {
      this.$emit('adicionar-contato');
    },
    excluirContato(id, tipo) {
      this.$emit('excluir-contato', id, tipo);
    }
  }
};
</script>
