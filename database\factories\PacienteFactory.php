<?php

namespace Database\Factories;

use App\Models\Paciente;
use App\Models\Clinica;
use App\Models\Dentista;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Paciente>
 */
class PacienteFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Paciente::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'nome' => fake()->name(),
            'data_nascimento' => fake()->date(),
            'cpf' => fake()->numerify('###.###.###-##'),
            'observacoes' => fake()->sentence(),
            'endereco_cep' => fake()->postcode(),
            'endereco_logradouro' => fake()->streetName(),
            'endereco_numero' => fake()->buildingNumber(),
            'endereco_cidade' => fake()->city(),
            'endereco_estado' => fake()->stateAbbr(),
            'public_token' => fake()->uuid(),
            'id_ficha' => fake()->randomNumber(4),
            'clinica_id' => Clinica::factory(),
            'dentista_id' => null, // Can be set when needed
            'profile_picture_url' => null,
            'formulario_respondido' => false,
            'consultas_realizadas' => 0,
        ];
    }
}
