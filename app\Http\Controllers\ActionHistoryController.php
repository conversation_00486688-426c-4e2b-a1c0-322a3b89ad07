<?php

namespace App\Http\Controllers;

use App\Models\ActionHistory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ActionHistoryController extends Controller
{
    /**
     * Display a listing of action histories.
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->payload();

        $query = ActionHistory::with(['user', 'paciente', 'dentista', 'clinica'])
            ->orderBy('created_at', 'desc');

        // Apply filters based on user permissions
        if (!$user['system_admin']) {
            // Non-admin users can only see actions from their clinic
            $query->where('clinica_id', $user['clinica']['id']);
        }

        // Apply additional filters from request
        if ($request->has('action_type')) {
            $query->byActionType($request->action_type);
        }

        if ($request->has('user_id')) {
            $query->byUser($request->user_id);
        }

        if ($request->has('user_search')) {
            $query->byUserSearch($request->user_search);
        }

        if ($request->has('paciente_id')) {
            $query->byPaciente($request->paciente_id);
        }

        if ($request->has('dentista_id')) {
            $query->byDentista($request->dentista_id);
        }

        if ($request->has('entity_type')) {
            $query->byEntityType($request->entity_type);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        // Pagination
        $perPage = $request->get('per_page', 50);
        $histories = $query->paginate($perPage);

        return response()->json($histories);
    }

    /**
     * Display the specified action history.
     */
    public function show(ActionHistory $actionHistory): JsonResponse
    {
        $user = auth()->payload();

        // Check permissions
        if (!$user['system_admin'] && $actionHistory->clinica_id !== $user['clinica']['id']) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $actionHistory->load(['user', 'paciente', 'dentista', 'clinica']);

        return response()->json($actionHistory);
    }

    /**
     * Get action histories for a specific patient.
     */
    public function getByPatient(Request $request, int $patientId): JsonResponse
    {
        $user = auth()->payload();

        $query = ActionHistory::with(['user', 'paciente', 'dentista', 'clinica'])
            ->byPaciente($patientId)
            ->orderBy('created_at', 'desc');

        // Apply permissions
        if (!$user['system_admin']) {
            $query->where('clinica_id', $user['clinica']['id']);
        }

        // Apply user search filter
        if ($request->has('user_search')) {
            $query->byUserSearch($request->user_search);
        }

        $perPage = $request->get('per_page', 50);
        $histories = $query->paginate($perPage);

        return response()->json($histories);
    }

    /**
     * Get action histories for a specific dentist.
     */
    public function getByDentist(Request $request, int $dentistId): JsonResponse
    {
        $user = auth()->payload();

        $query = ActionHistory::with(['user', 'paciente', 'dentista', 'clinica'])
            ->byDentista($dentistId)
            ->orderBy('created_at', 'desc');

        // Apply permissions
        if (!$user['system_admin']) {
            $query->where('clinica_id', $user['clinica']['id']);
        }

        // Apply user search filter
        if ($request->has('user_search')) {
            $query->byUserSearch($request->user_search);
        }

        $perPage = $request->get('per_page', 50);
        $histories = $query->paginate($perPage);

        return response()->json($histories);
    }

    /**
     * Get action histories for a specific user.
     */
    public function getByUser(Request $request, int $userId): JsonResponse
    {
        $user = auth()->payload();

        $query = ActionHistory::with(['user', 'paciente', 'dentista', 'clinica'])
            ->byUser($userId)
            ->orderBy('created_at', 'desc');

        // Apply permissions
        if (!$user['system_admin']) {
            $query->where('clinica_id', $user['clinica']['id']);
        }

        $perPage = $request->get('per_page', 50);
        $histories = $query->paginate($perPage);

        return response()->json($histories);
    }

    /**
     * Get action history statistics.
     */
    public function getStats(Request $request): JsonResponse
    {
        $user = auth()->payload();

        $query = ActionHistory::query();

        // Apply permissions
        if (!$user['system_admin']) {
            $query->where('clinica_id', $user['clinica']['id']);
        }

        // Apply date filter if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        $stats = [
            'total_actions' => $query->count(),
            'actions_by_type' => $query->selectRaw('action_type, COUNT(*) as count')
                ->groupBy('action_type')
                ->pluck('count', 'action_type'),
            'actions_by_user' => $query->with('user')
                ->selectRaw('user_id, COUNT(*) as count')
                ->groupBy('user_id')
                ->get()
                ->map(function ($item) {
                    return [
                        'user_id' => $item->user_id,
                        'user_name' => $item->user->name ?? 'Unknown',
                        'count' => $item->count,
                    ];
                }),
            'recent_actions' => ActionHistory::with(['user', 'paciente', 'dentista', 'clinica'])
                ->when(!$user['system_admin'], function ($q) use ($user) {
                    $q->where('clinica_id', $user['clinica']['id']);
                })
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get(),
        ];

        return response()->json($stats);
    }

    /**
     * Export action histories to CSV.
     */
    public function export(Request $request)
    {
        $user = auth()->payload();

        $query = ActionHistory::with(['user', 'paciente', 'dentista', 'clinica'])
            ->orderBy('created_at', 'desc');

        // Apply permissions
        if (!$user['system_admin']) {
            $query->where('clinica_id', $user['clinica']['id']);
        }

        // Apply filters
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        // Apply user search filter
        if ($request->has('user_search')) {
            $query->byUserSearch($request->user_search);
        }

        $histories = $query->get();

        $filename = 'action_histories_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function () use ($histories) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Date/Time',
                'User',
                'Action Type',
                'Description',
                'Entity Type',
                'Patient',
                'Dentist',
                'Clinica',
                'HTTP Method',
                'Endpoint',
            ]);

            foreach ($histories as $history) {
                fputcsv($file, [
                    $history->id,
                    $history->created_at->format('Y-m-d H:i:s'),
                    $history->user->name ?? 'Unknown',
                    $history->action_type,
                    $history->action_description,
                    $history->entity_type,
                    $history->paciente->nome ?? '',
                    $history->dentista->nome ?? '',
                    $history->clinica->nome ?? '',
                    $history->http_method,
                    $history->endpoint,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
