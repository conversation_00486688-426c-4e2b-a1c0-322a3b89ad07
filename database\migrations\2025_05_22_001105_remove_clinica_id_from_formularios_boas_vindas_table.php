<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First try to drop the foreign key constraint if it exists
        try {
            Schema::table('formularios_boas_vindas', function (Blueprint $table) {
                $table->dropForeign(['clinica_id']);
            });
        } catch (\Exception $e) {
            // Ignore if the foreign key doesn't exist
        }

        // Then drop the column
        Schema::table('formularios_boas_vindas', function (Blueprint $table) {
            $table->dropColumn('clinica_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('formularios_boas_vindas', function (Blueprint $table) {
            $table->foreignId('clinica_id')->after('id')->constrained('clinicas');
        });
    }
};
