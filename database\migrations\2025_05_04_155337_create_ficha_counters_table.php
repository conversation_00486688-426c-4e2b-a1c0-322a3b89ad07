<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('ficha_counters', function (Blueprint $table) {
            $table->unsignedBigInteger('clinica_id')->primary(); // PK sem auto-increment
            $table->unsignedBigInteger('last_id_ficha')->default(0);
            $table->timestamps();

            $table->foreign('clinica_id')->references('id')->on('clinicas')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('ficha_counters');
    }
};