# ActionHistory System Documentation

## Overview

The ActionHistory system provides comprehensive tracking of all CREATE, UPDATE, and DELETE operations in the lumi-api project. It automatically logs user actions with detailed information about what changed, who made the change, and when it occurred.

## Features

- **Automatic Logging**: All CUD operations are automatically tracked via middleware
- **Flexible Entity Relationships**: Supports Patient, Dentist, Clinica, and future entity types
- **Data State Tracking**: Stores both "before" and "after" states for updates
- **User Attribution**: Always tracks which user performed the action
- **HTTP Context**: Captures HTTP method, endpoint, IP address, and user agent
- **Performance Optimized**: Designed to not interfere with main business logic
- **Comprehensive API**: Full REST API for querying and exporting action history

## Database Schema

### action_histories Table

| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| user_id | bigint | User who performed the action (required) |
| action_type | string | CREATE, UPDATE, or DELETE |
| action_description | text | Human-readable description |
| http_method | string | POST, PUT, PATCH, DELETE |
| endpoint | string | API endpoint path |
| paciente_id | bigint | Related patient (nullable) |
| dentista_id | bigint | Related dentist (nullable) |
| clinica_id | bigint | Related clinica (nullable) |
| entity_type | string | Model class name for polymorphic relations |
| entity_id | bigint | Model ID for polymorphic relations |
| old_data | json | Data before change (for updates/deletes) |
| new_data | json | Data after change (for creates/updates) |
| ip_address | string | User's IP address |
| user_agent | text | User's browser/client info |
| created_at | timestamp | When the action occurred |
| updated_at | timestamp | Record update time |

## Implementation Components

### 1. ActionHistory Model
- Located: `app/Models/ActionHistory.php`
- Provides relationships to User, Patient, Dentist, Clinica
- Includes scopes for filtering and querying
- Helper methods for analyzing changes

### 2. ActionHistoryService
- Located: `app/Services/ActionHistoryService.php`
- Core service for logging actions
- Handles data sanitization and relationship extraction
- Generates human-readable descriptions

### 3. ActionHistoryMiddleware
- Located: `app/Http/Middleware/ActionHistoryMiddleware.php`
- Automatically applied to authenticated routes
- Filters out non-CUD operations and excluded endpoints

### 4. LogsActionHistory Trait
- Located: `app/Traits/LogsActionHistory.php`
- Provides easy-to-use methods for controllers
- Handles user ID extraction from JWT
- Supports bulk operations

### 5. ActionHistoryController
- Located: `app/Http/Controllers/ActionHistoryController.php`
- REST API for querying action history
- Includes filtering, pagination, and export functionality

## Usage Examples

### Automatic Logging (Recommended)

The system automatically logs actions when using the middleware. Controllers using the `LogsActionHistory` trait can easily add logging:

```php
// In a controller method
public function store(Request $request)
{
    $entity = Model::create($request->all());

    // Log the creation
    $this->logCreateAction($entity, null, $request, "Created new entity");

    return response()->json($entity);
}

public function update(Request $request, $id)
{
    $entity = Model::find($id);
    $originalData = $this->captureOriginalData($entity);

    $entity->update($request->all());

    // Log the update
    $this->logUpdateAction($entity, $originalData, null, $request);

    return response()->json($entity);
}
```

### Manual Logging

For custom scenarios, you can use the service directly:

```php
use App\Services\ActionHistoryService;

$actionHistoryService = app(ActionHistoryService::class);
$actionHistoryService->logCreate($userId, $entity, $newData, $request);
```

## API Endpoints

### Get Action History
```
GET /action-history
```
Query parameters:
- `action_type`: Filter by CREATE, UPDATE, DELETE
- `user_id`: Filter by user
- `user_search`: Search by username (case-insensitive, with wildcards)
- `paciente_id`: Filter by patient
- `dentista_id`: Filter by dentist
- `entity_type`: Filter by model type
- `start_date` & `end_date`: Date range filter
- `per_page`: Pagination size

### Get Action History by Entity
```
GET /action-history/patient/{paciente_id}
GET /action-history/dentist/{dentista_id}
GET /action-history/user/{user_id}
```

### Get Statistics
```
GET /action-history/stats
```

### Export to CSV
```
GET /action-history/export
```

## Security & Permissions

- **Clinic Isolation**: Non-admin users only see actions from their clinic
- **System Admin Access**: System admins can see all actions across clinics
- **Data Sanitization**: Sensitive fields (passwords, tokens) are automatically removed
- **Size Limits**: Large text fields are truncated to prevent database issues

## Performance Considerations

- **Asynchronous Logging**: Action logging failures don't break main operations
- **Indexed Queries**: Database indexes on frequently queried columns
- **Data Retention**: Consider implementing data retention policies for large datasets
- **Bulk Operations**: Special handling for bulk operations to avoid excessive logging

## Configuration

### Excluded Endpoints

The middleware automatically skips these patterns:
- `auth/*` (login, logout, refresh)
- `img/*` (image serving)
- `modelo3d/*` (3D model serving)
- Password reset endpoints

### Data Sanitization

Automatically removes these sensitive fields:
- `password`
- `password_confirmation`
- `remember_token`
- `api_token`

## Migration

To set up the ActionHistory system:

1. Run the migration:
```bash
php artisan migrate
```

2. The system is automatically active for all authenticated routes with the middleware applied.

## Monitoring & Maintenance

### Regular Tasks
- Monitor action_histories table size
- Archive old records if needed
- Review failed logging attempts in application logs

### Troubleshooting
- Check application logs for ActionHistory service errors
- Verify middleware is applied to routes
- Ensure user authentication is working properly

## Future Enhancements

- **Real-time Notifications**: WebSocket integration for live action feeds
- **Advanced Analytics**: Dashboards and reporting features
- **Data Retention Policies**: Automatic cleanup of old records
- **Audit Compliance**: Enhanced features for regulatory compliance
- **Performance Metrics**: Track system performance impact
