<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $table = 'pacientes';

    public function up(): void
    {
        Schema::table($this->table, function (Blueprint $table) {
            $table->dropColumn('id_dentista');
            $table->dropColumn('id_convenio');
            $table->dropColumn('indicado_por');
        });
    }
    
    public function down(): void
    {
        Schema::table($this->table, function (Blueprint $table) {
            $table->integer('id_dentista');
            $table->integer('id_convenio');
            $table->string('indicado_por');
        });
    }
};