<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class FinanceiroPagar extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'financeiro_pagar';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'caixa_id',
        'conta_id',
        'clinica_id',
        'pagador_nome',
        'pagador_tipo',
        'fornecedor_id',
        'paciente_id',
        'contrato_codigo',
        'referencia',
        'descricao',
        'notas',
        'valor_nominal',
        'descontos',
        'acrescimos',
        'valor_final',
        'data_emissao',
        'data_vencimento',
        'data_pagamento',
        'meio_pagamento',
        'parcela',
        'parcelas_total',
        'status',
        'lancado_por',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'nome'
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    public function clinica(): HasOne
    {
        return $this->hasOne(Clinica::class, 'id', 'clinica_id');
    }
}
