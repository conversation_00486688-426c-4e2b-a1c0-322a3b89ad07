<?php

namespace App\Providers;

use App\Services\ActionHistoryService;
use Illuminate\Support\ServiceProvider;

class ActionHistoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(ActionHistoryService::class, function ($app) {
            return new ActionHistoryService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
