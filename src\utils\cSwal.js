import Swal from 'sweetalert2'

Swal.cAlert = function (html, options = {}) {
    const defaultOptions = {
        html,
    }

    options = { ...defaultOptions, ...options }

    return Swal.fire(options)
}

Swal.loading = function (text = 'Carregando...', options = {}) {
    const defaultOptions = {
        html: '<div class="spinner-border text-primary" role="status"></div><br><br>' + text,
        allowOutsideClick: false,
        allowEscapeKey: false,
        allowEnterKey: false,
        showConfirmButton: false,
        showCancelButton: false,
    };

    options = { ...defaultOptions, ...options };

    return Swal.fire(options);
};

Swal.loaded = function () {
    return Swal.close();
};

Swal.cInfo = function (html, options = {}) {
    const defaultOptions = {
        html,
        icon: 'info',
    }

    options = { ...defaultOptions, ...options }

    return Swal.fire(options)
}

Swal.cConfirm = function (html, callback, options = {}) {
    const defaultOptions = {
        html,
        icon: 'warning',
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: 'SIM',
        cancelButtonText: 'NÃO',
    }

    options = { ...defaultOptions, ...options }

    return Swal.fire(options)
        .then((result) => {
            if (result.isConfirmed) {
                callback()
            }
        });
}

Swal.cWarning = function (html, options = {}) {
    const defaultOptions = {
        html,
        icon: 'warning',
    }

    options = { ...defaultOptions, ...options }

    return Swal.fire(options)
}

Swal.cError = function (html, options = {}) {
    const defaultOptions = {
        html,
        title: 'Ops...',
        icon: 'error',
    }

    options = { ...defaultOptions, ...options }

    return Swal.fire(options)
}

Swal.cSuccess = function (html, options = {}) {
    const defaultOptions = {
        html,
        title: getSuccessMessage(),
        icon: 'success',
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true,
        showClass: {
            popup: 'swal2-show'
        },
        hideClass: {
            popup: 'swal2-hide'
        }
    }

    options = { ...defaultOptions, ...options }

    return Swal.fire(options)
}

export default Swal

function getSuccessMessage() {
    var mensagens = [
        'Tudo em ordem!',
        'Prontinho!',
        'Missão completa!',
        'Deu certinho!',
        'Sucesso total!',
        'Finalizado com sucesso!',
        'Está feito!',
        'Missão cumprida!',
        'Tudo nos conformes!',
        'Feito com capricho!',
        'Prontinho!',
        'Tudo certo!',
        'Concluído sem erro!',
        'Tudo sob controle!',
        'Finalizado com sucesso!',
        'Ação completada!',
        'Perfeito, deu certo!',
        'Tudo resolvido!',
        'Concluído com precisão!',
        'Feito com excelência!',
        'Sucesso na operação!',
        'Tudo certo por aqui!',
        'Executado com perfeição!',
        'Concluído com êxito!',
    ];

    // Generate a random index
    var randomIndex = Math.floor(Math.random() * mensagens.length);

    // Return the message at the random index
    return mensagens[randomIndex];
}