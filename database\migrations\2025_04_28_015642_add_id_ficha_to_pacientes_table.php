<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration // Classe AddIdFichaToPacientesTable
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pacientes', function (Blueprint $table) {
            $table->unsignedBigInteger('id_ficha') // Define o tipo da coluna
                  ->nullable()                     // Permite valores nulos
                  ->after('id')                    // Posiciona após a coluna 'id' (ajuste se necessário)
                  ->index();                       // Adiciona um índice para otimizar buscas
            
            // Se precisar definir como chave estrangeira, descomente e ajuste a linha abaixo:
            // $table->foreign('id_ficha')->references('id')->on('fichas')->onDelete('set null'); // Exemplo: referenciando 'fichas'
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pacientes', function (Blueprint $table) {
            // Remove a chave estrangeira primeiro, se ela foi adicionada no método 'up'
            // $table->dropForeign(['id_ficha']);

            // Remove o índice antes de remover a coluna
            $table->dropIndex(['{id_ficha}_index']); // O nome do índice padrão é nomeTabela_nomeColuna_index

            // Remove a coluna
            $table->dropColumn('id_ficha');
        });
    }
};
