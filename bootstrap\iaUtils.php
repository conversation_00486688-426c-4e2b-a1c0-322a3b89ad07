<?php

function iaPrompt($input)
{
    $apiUrl = 'https://api.groq.com/openai/v1/chat/completions';
    $apiKey = '********************************************************';
    $model = 'llama3-8b-8192';
    $mensagem = $input;

    $headers = [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json; charset=utf-8'
    ];

    $dados = [
        'messages' => [
            [
                'role' => 'user',
                'content' => $mensagem
            ]
        ],
        'model' => $model
    ];

    $jsonDados = json_encode($dados);

    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonDados);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $resposta = curl_exec($ch);
    curl_close($ch);

    $resposta = json_decode($resposta, true);

    if (isset($resposta['choices'][0]['message']['content'])) {
        $resposta = $resposta['choices'][0]['message']['content'];
    } else {
        $resposta = null;
    }

    return $resposta;
}
