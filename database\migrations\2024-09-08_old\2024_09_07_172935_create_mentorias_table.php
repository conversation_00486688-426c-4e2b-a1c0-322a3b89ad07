<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMentoriasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mentorias', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('solicitante_id');
            $table->unsignedBigInteger('mentor_id');
            $table->text('observacao')->nullable();
            $table->string('status')->default('PENDENTE');
            $table->timestamps();

            $table->foreign('solicitante_id')->references('id')->on('dentistas');
            $table->foreign('mentor_id')->references('id')->on('dentistas');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mentorias');
    }
}