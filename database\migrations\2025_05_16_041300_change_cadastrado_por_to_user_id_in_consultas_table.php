<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Primeiro, verificamos se a coluna cadastrado_por existe
        if (Schema::hasColumn('consultas', 'cadastrado_por')) {
            // Criamos uma coluna temporária para armazenar os valores
            Schema::table('consultas', function (Blueprint $table) {
                $table->unsignedBigInteger('user_id_temp')->nullable()->after('cadastrado_por');
            });

            // Copiamos os dados da coluna antiga para a nova
            DB::statement('UPDATE consultas SET user_id_temp = cadastrado_por');

            // Removemos a coluna antiga
            Schema::table('consultas', function (Blueprint $table) {
                $table->dropColumn('cadastrado_por');
            });

            // Renomeamos a coluna temporária para o nome final
            Schema::table('consultas', function (Blueprint $table) {
                $table->renameColumn('user_id_temp', 'user_id');
            });
        } else if (!Schema::hasColumn('consultas', 'user_id')) {
            // Se a coluna cadastrado_por não existe e user_id também não, criamos a coluna user_id
            Schema::table('consultas', function (Blueprint $table) {
                $table->unsignedBigInteger('user_id')->nullable();
            });
        }

        // Adicionamos a foreign key e o índice
        Schema::table('consultas', function (Blueprint $table) {
            // Tentamos adicionar a foreign key, ignorando erros se já existir
            try {
                $table->foreign('user_id')
                      ->references('id')
                      ->on('users')
                      ->onDelete('set null');
            } catch (\Exception $e) {
                // A foreign key provavelmente já existe, então ignoramos o erro
            }

            // Tentamos adicionar o índice, ignorando erros se já existir
            try {
                $table->index('user_id');
            } catch (\Exception $e) {
                // O índice provavelmente já existe, então ignoramos o erro
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consultas', function (Blueprint $table) {
            // Removemos a foreign key e o índice
            $table->dropForeign(['user_id']);
            $table->dropIndex(['user_id']);

            // Renomeamos a coluna para o nome antigo
            $table->renameColumn('user_id', 'cadastrado_por');
        });
    }

    // Removemos os métodos que usavam getDoctrineSchemaManager()
};
