<?php

namespace App\Http\Controllers;

use App\Models\ContatoPaciente;
use Illuminate\Http\Request;

class ContatoPacienteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $contatoPaciente = new ContatoPaciente();
        $contatoPaciente->paciente_id = $request->input('paciente_id');
        $contatoPaciente->tipo = $request->input('tipo');
        $contatoPaciente->contato = $request->input('contato');
        $contatoPaciente->descricao = $request->input('descricao');

        $contatoPaciente->save();

        // Retorne uma resposta, por exemplo:
        return responseSuccess();
    }

    public function deleteContato(Request $request, $id)
    {
        try {
            // Recupera o contato paciente pelo ID
            $contatoPaciente = ContatoPaciente::find($id);

            // Verifica se o contato paciente existe
            if (!$contatoPaciente)
                throw new \Exception('Contato não encontrado');

            // Remove o contato paciente do banco de dados
            $contatoPaciente->delete();

            // Retorna uma resposta de sucesso
            $response = responseSuccess();
        } catch (Exception $e) {
            $response = responseError([
                'message' => $e->getMessage()
            ]);
        }

        return $response;
    }

    /**
     * Display the specified resource.
     */
    public function show(ContatoPaciente $contatoPaciente)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ContatoPaciente $contatoPaciente)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ContatoPaciente $contatoPaciente)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContatoPaciente $contatoPaciente)
    {
        $contatoPaciente->delete();
    }
}
