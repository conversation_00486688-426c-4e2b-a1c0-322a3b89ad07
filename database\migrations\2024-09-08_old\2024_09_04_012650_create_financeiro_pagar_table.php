<?php

// database/migrations/YYYYMMDDHHMMSS_create_financeiro_pagar_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateFinanceiroPagarTable extends Migration
{
    public function up()
    {
        Schema::create('financeiro_pagar', function (Blueprint $table) {
            $table->id();
            $table->integer('caixa_id')->unsigned()->nullable();
            $table->integer('conta_id')->unsigned()->nullable();
            $table->string('recebedor_nome')->nullable();
            $table->string('recebedor_tipo')->nullable();
            $table->integer('fornecedor_id')->unsigned()->nullable();
            $table->integer('paciente_id')->unsigned()->nullable();
            $table->integer('contrato_codigo')->unsigned()->nullable();
            $table->tinyText('referencia')->nullable();
            $table->string('descricao')->nullable();
            $table->string('notas')->nullable();
            $table->decimal('valor_nominal', 10, 2)->nullable();
            $table->decimal('descontos', 10, 2)->nullable();
            $table->decimal('acrescimos', 10, 2)->nullable();
            $table->decimal('valor_final', 10, 2)->nullable();
            $table->date('data_emissao')->nullable();
            $table->date('data_vencimento')->nullable();
            $table->date('data_pagamento')->nullable();
            $table->tinyText('meio_pagamento')->nullable();
            $table->integer('parcela')->unsigned()->nullable();
            $table->integer('parcelas_total')->unsigned()->nullable();
            $table->string('status')->nullable();
            $table->integer('lancado_por')->unsigned()->default(0)->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('financeiro_pagar');
    }
}