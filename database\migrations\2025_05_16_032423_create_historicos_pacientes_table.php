<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('historicos_pacientes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('paciente_id')->constrained('pacientes')->onDelete('cascade');
            $table->foreignId('consulta_id')->nullable()->constrained('consultas')->onDelete('set null');
            $table->date('data');
            $table->time('horario');
            $table->string('codigo_acao');
            $table->text('descricao');
            $table->json('modificacoes')->nullable();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Índices para melhorar a performance de consultas
            $table->index('paciente_id');
            $table->index('consulta_id');
            $table->index('user_id');
            $table->index('data');
            $table->index('codigo_acao');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('historicos_pacientes');
    }
};
