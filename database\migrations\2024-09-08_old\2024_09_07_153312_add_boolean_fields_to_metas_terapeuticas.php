<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddBooleanFieldsToMetasTerapeuticas extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('metas_terapeuticas', function (Blueprint $table) {
            $table->dropColumn('concluida');
            $table->string('status')->default('PENDENTE');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('metas_terapeuticas', function (Blueprint $table) {
            $table->boolean('concluida')->default(0);
            $table->dropColumn('status');
        });
    }
}
