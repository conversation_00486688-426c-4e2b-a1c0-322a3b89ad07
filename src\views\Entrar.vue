<template>
  <div class="page-header align-items-start min-vh-100" v-bind:style="backgroundImage">
    <span class="mask bg-gradient-dark opacity-6"></span>

    <!-- Login submission loading overlay -->
    <div
      v-if="isLoggingIn"
      class="login-loading-overlay"
      :class="{ 'show': isLoggingIn }"
    ></div>

    <div class="container my-auto">
      <div class="row">
        <div class="col-lg-4 col-md-8 col-12 mx-auto">
          <div class="card z-index-0 fadeIn3 fadeInBottom login-card" :class="{ 'logging-in': isLoggingIn }">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
              <div
                class="shadow-secondary border-radius-lg py-3 bg-gradient-lumi"
                style="border: 1px solid #d2d2d2; padding-left: 35px"
              >
                <img :src="LumiBlueLogo" class="login-page-logo" />
              </div>
            </div>
            <div class="card-body">
              <form role="form" class="text-start mt-3" @submit.prevent="submitLogin">
                <div class="mb-3">
                  <MaterialInput
                    id="username"
                    type="text"
                    label="Usuário"
                    name="username"
                    v-model="credentials.username"
                  />
                </div>
                <div class="mb-3">
                  <MaterialInput
                    id="senha"
                    type="password"
                    label="Senha"
                    v-model="credentials.password"
                    name="senha"
                  />
                </div>
                <material-switch
                  id="rememberMe"
                  name="rememberMe"
                  :checked="rememberDevice"
                  @change="rememberDevice = $event"
                  >Manter este dispositivo conectado</material-switch
                >
                <div class="text-center">
                  <material-button
                    class="my-4 mb-2"
                    variant="gradient"
                    color="secondary"
                    fullWidth
                    :loading="isLoggingIn"
                    :loadingText="$t('login.loggingIn')"
                  >
                    {{ $t("login.submitAction") }}
                  </material-button>
                </div>
                <div class="mt-4 text-sm text-center w-100">
                  <a
                    href="https://lumiorthosystem.com.br"
                    class="text-decoration-none"
                    target="_blank"
                  >
                    <div>Ainda não é cliente?</div>
                    <div><b>Conheça-nos</b></div>
                  </a>
                </div>
                  <!-- <router-link
                    :to="{ name: 'SignUp' }"
                    class="text-success text-gradient font-weight-bold"
                    >Sign up</router-link
                  > -->
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <footer class="footer position-absolute bottom-2 py-2 w-100">
      <div class="container">
        <div class="row align-items-center justify-content-lg-between">
          <div class="col-12 my-auto">
            <div
              class="copyright text-center text-sm text-white text-lg-start d-flex flex-column"
              style="font-weight: 400"
            >
              <span style="font-size: 11pt"
                >© {{ new Date().getFullYear() }} Lumi Plan</span
              >
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>
<style scoped>
@media (min-width: 960px) {
  .card {
    min-width: 370px;
  }
}

/* Adicionar margens laterais no mobile para evitar que o card cole nas bordas */
@media (max-width: 767.98px) {
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Garantir que o card tenha um espaçamento mínimo das bordas */
  .login-card {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
}

/* Corrigir cor do switch para azul */
:deep(.form-switch .form-check-input:checked) {
  background-color: #56809F !important;
  border-color: #56809F !important;
}

:deep(.form-switch .form-check-input:checked:after) {
  border-color: #56809F !important;
}

/* Login submission loading overlay */
.login-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.25);
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out;
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
}

.login-loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Animações de loading */
.login-card {
  transition: all 0.3s ease;
}

.login-card.logging-in {
  transform: scale(0.98);
  opacity: 0.9;
}

.login-card.logging-in .card-body {
  pointer-events: none;
}

/* Efeito de pulse no logo durante o loading */
.login-card.logging-in .login-page-logo {
  animation: loginPulse 1.5s ease-in-out infinite;
}

@keyframes loginPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}
</style>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import MaterialSwitch from "@/components/MaterialSwitch.vue";
import MaterialButton from "@/components/MaterialButton.vue";
import { mapMutations } from "vuex";
import whiteConsultory from "@/assets/img/lumi/whiteConsultory.png";
import LumiBlueLogo from "@/assets/img/lumi/logo-blue.png";
import usuariosService from "@/services/usuariosService.js";
import router from "../router/index.js";
import cSwal from "@/utils/cSwal.js";

const credentials = {
  username: "",
  password: "",
};

export default {
  name: "login",
  components: {
    MaterialInput,
    MaterialSwitch,
    MaterialButton,
  },
  data() {
    return {
      credentials,
      LumiBlueLogo,
      rememberDevice: false,
      isLoggingIn: false,
    };
  },
  mounted() {
    if (usuariosService.isAuthenticated()) {
      router.push({ path: "agenda" });
    }
  },
  methods: {
    ...mapMutations(["toggleEveryDisplay", "toggleHideConfig"]),
    async submitLogin() {
      if (this.isLoggingIn) return; // Previne múltiplos submits

      this.isLoggingIn = true;

      try {
        const auth = await usuariosService.login(this.credentials);

        if (auth) {
          // Pequeno delay para mostrar o feedback visual
          setTimeout(() => {
            this.$router.go("/agenda");
          }, 500);
        } else {
          this.isLoggingIn = false;
          cSwal.cError("Usuário ou senha incorretos.");
        }
      } catch (error) {
        this.isLoggingIn = false;
        cSwal.cError("Erro ao fazer login. Tente novamente.");
      }
    },
  },
  computed: {
    backgroundImage() {
      return {
        backgroundImage: `url(${whiteConsultory})`,
        transform: "scale(1.05)",
      };
    },
  },
  beforeMount() {
    this.toggleEveryDisplay();
    this.toggleHideConfig();
  },
  beforeUnmount() {
    this.toggleEveryDisplay();
    this.toggleHideConfig();
  },
};
</script>
