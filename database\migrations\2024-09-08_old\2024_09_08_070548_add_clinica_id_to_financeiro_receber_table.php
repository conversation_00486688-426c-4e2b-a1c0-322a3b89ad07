<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddClinicaIdToFinanceiroReceberTable extends Migration
{
    public function up()
    {
        Schema::table('financeiro_receber', function (Blueprint $table) {
            $table->unsignedBigInteger('clinica_id');
            $table->foreign('clinica_id')->references('id')->on('clinicas');
        });
    }

    public function down()
    {
        Schema::table('financeiro_receber', function (Blueprint $table) {
            $table->dropForeign('financeiro_receber_clinica_id_foreign');
            $table->dropColumn('clinica_id');
        });
    }
}
