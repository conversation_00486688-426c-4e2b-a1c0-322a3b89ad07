import axios from '@/services/axios'
import moment from 'moment'

// Cache para modelos já baixados
const modelosCache = new Map();

export async function uploadModelo3D(options) {
    if (!options.paciente_id)
        return false;

    options = {
        dir: 'modelo3d',
        data: moment().format('YYYY-MM-DD HH:mm:ss'),
        descricao: '',
        tipo: 'stl',
        ...options,
    }

    try {
        let data = new FormData();
        data.append('paciente_id', options.paciente_id);
        data.append('modelo3d', options.modelo); // Alterado para 'modelo3d' conforme implementação do backend
        data.append('dir', options.dir);
        data.append('data', options.data);
        data.append('descricao', options.descricao);
        data.append('tipo', options.tipo);

        const response = await axios.post('/modelo3d', data, // Alterado para '/modelo3d' conforme implementação do backend
            {
                headers: { 'Content-Type': 'multipart/form-data' }
            })

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao enviar modelo 3D:', error);
    }

    return false;
}

export async function getModelos3D(pacienteId) {
    try {
        // Usando a rota específica implementada pelo backend para buscar modelos 3D de um paciente
        const response = await axios.get(`/paciente/${pacienteId}/modelos3d`);

        if (!response || !response.data)
            return [];

        // O backend retorna diretamente o array de modelos 3D
        return response.data || [];

    } catch (error) {
        console.error('Erro ao buscar modelos 3D:', error);
        return [];
    }
}

export async function excluirModelo3D(id) {
    try {
        // Assumindo que a rota de exclusão segue o mesmo padrão da rota de upload
        const response = await axios.delete(`/modelo3d/${id}`);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return true;

    } catch (error) {
        console.error('Erro ao excluir modelo 3D:', error);
        return false;
    }
}

/**
 * Baixa um modelo 3D a partir da URL
 * @param {string} url - URL do modelo 3D
 * @param {boolean} forceRefresh - Se true, ignora o cache e baixa novamente
 * @returns {Promise<ArrayBuffer>} - ArrayBuffer contendo os dados do modelo
 */
export async function downloadModelo3D(url, forceRefresh = false) {
    // Verificar se o modelo já está em cache
    if (!forceRefresh && modelosCache.has(url)) {
        return modelosCache.get(url);
    }

    try {
        // Fazer o download do modelo
        const response = await axios.get(url, {
            responseType: 'arraybuffer',
            headers: {
                'Accept': '*/*'
            }
        });

        if (!response || !response.data) {
            throw new Error('Falha ao baixar o modelo 3D');
        }

        // Armazenar no cache
        modelosCache.set(url, response.data);

        return response.data;
    } catch (error) {
        console.error('Erro ao baixar modelo 3D:', error);
        throw error;
    }
}
