<?php

// Arquivo: database/migrations/*_alterar_tabela_questoes_formulario_boas_vindas.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterarTabelaQuestoesFormularioBoasVindas extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('questoes_formulario_boas_vindas', function (Blueprint $table) {
            // Renomear colunas para o plural
            $table->renameColumn('ponto_negativo', 'pontos_negativos');
            $table->renameColumn('ponto_neutro', 'pontos_neutros');
            $table->renameColumn('ponto_positivo', 'pontos_positivos');
            $table->renameColumn('resposta', 'respostas');

            // Adicionar coluna 'ponto_atencao' antes de 'created_at'
            $table->text('ponto_atencao')->nullable()->before('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('questoes_formulario_boas_vindas', function (Blueprint $table) {
            // Remover a coluna 'ponto_atencao'
            $table->dropColumn('ponto_atencao');

            // Renomear colunas de volta ao singular
            $table->renameColumn('pontos_negativos', 'ponto_negativo');
            $table->renameColumn('pontos_neutros', 'ponto_neutro');
            $table->renameColumn('pontos_positivos', 'ponto_positivo');
            $table->renameColumn('respostas', 'resposta');
        });
    }
}