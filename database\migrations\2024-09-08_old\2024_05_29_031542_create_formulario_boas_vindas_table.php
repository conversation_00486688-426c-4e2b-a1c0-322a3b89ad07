<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('formularios_boas_vindas', function (Blueprint $table) {
            $table->id();
            $table->datetime('horario');
            $table->integer('id_paciente')->nullable();
            $table->string('nome');
            $table->string('email');
            $table->string('whatsapp');
            $table->text('dados');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('formularios_boas_vindas');
    }
};
