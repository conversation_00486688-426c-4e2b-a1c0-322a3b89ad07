<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fator_diagnostico_paciente', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('fator_diagnostico_id');
            $table->unsignedBigInteger('paciente_id');

            $table->foreign('fator_diagnostico_id')->references('id')
                ->on('fatores_diagnostico')->onDelete('cascade');
            $table->foreign('paciente_id')->references('id')
                ->on('pacientes')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fator_diagnostico_paciente');
    }
};
