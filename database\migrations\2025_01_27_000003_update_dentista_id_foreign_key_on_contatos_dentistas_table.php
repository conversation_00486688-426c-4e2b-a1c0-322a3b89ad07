<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contatos_dentistas', function (Blueprint $table) {
            // Drop the existing foreign key
            $table->dropForeign(['dentista_id']);
            
            // Add the foreign key with CASCADE on delete
            $table->foreign('dentista_id')
                  ->references('id')
                  ->on('dentistas')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contatos_dentistas', function (Blueprint $table) {
            // Drop the foreign key with CASCADE
            $table->dropForeign(['dentista_id']);
            
            // Restore the original foreign key without CASCADE
            $table->foreign('dentista_id')
                  ->references('id')
                  ->on('dentistas');
        });
    }
};
