# Filtro user_search - Exemplos de Uso

## Visão Geral

O filtro `user_search` foi implementado no sistema ActionHistory para permitir busca case-insensitive por username dos usuários que realizaram ações no sistema.

## Características do Filtro

- **Case-insensitive**: Busca independente de maiúsculas/minúsculas
- **Wildcards**: Usa `%` automaticamente para busca parcial
- **Compatível com MySQL/MariaDB**: Usa `LOWER()` e `LIKE` para compatibilidade

## Endpoints que Suportam o Filtro

Todos os endpoints de ActionHistory suportam o parâmetro `user_search`:

1. `GET /action-history` - Lista geral
2. `GET /action-history/patient/{id}` - Por paciente
3. `GET /action-history/dentist/{id}` - Por dentista
4. `GET /action-history/user/{id}` - Por usuário
5. `GET /action-history/export` - Exportação CSV

## Exemplos de Uso

### 1. Busca Simples por Username

```http
GET /action-history?user_search=daniel
```

**Resultado:** Re<PERSON>na todas as ações realizadas por usuários que tenham "daniel" no username (ex: "daniel.salles", "daniela.costa", etc.)

### 2. Busca Case-Insensitive

```http
GET /action-history?user_search=DANIEL
```

**Resultado:** Mesmo resultado do exemplo anterior, pois a busca é case-insensitive.

### 3. Busca Parcial

```http
GET /action-history?user_search=dan
```

**Resultado:** Retorna ações de usuários como "daniel.salles", "jordan.silva", "dandara.lima", etc.

### 4. Combinando com Outros Filtros

```http
GET /action-history?user_search=admin&action_type=DELETE&start_date=2025-01-01
```

**Resultado:** Ações de DELETE realizadas por usuários com "admin" no username desde 01/01/2025.

### 5. Busca em Histórico de Paciente Específico

```http
GET /action-history/patient/123?user_search=dr.silva
```

**Resultado:** Ações realizadas no paciente 123 por usuários que tenham "dr.silva" no username.

### 6. Exportação Filtrada

```http
GET /action-history/export?user_search=dentista&start_date=2025-01-01&end_date=2025-01-31
```

**Resultado:** Arquivo CSV com ações realizadas por usuários com "dentista" no username durante janeiro de 2025.

## Exemplos de Response

### Busca Bem-Sucedida

```json
{
    "data": [
        {
            "id": 1,
            "user_id": 32,
            "action_type": "CREATE",
            "action_description": "Created new patient: João Silva",
            "user": {
                "id": 32,
                "name": "Daniel Salles",
                "username": "daniel.salles"
            },
            "created_at": "2025-01-27T10:30:00.000000Z"
        }
    ],
    "links": {
        "first": "http://localhost/action-history?user_search=daniel&page=1",
        "last": "http://localhost/action-history?user_search=daniel&page=1",
        "prev": null,
        "next": null
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 1,
        "per_page": 50,
        "to": 1,
        "total": 1
    }
}
```

### Busca Sem Resultados

```json
{
    "data": [],
    "links": {
        "first": "http://localhost/action-history?user_search=inexistente&page=1",
        "last": "http://localhost/action-history?user_search=inexistente&page=1",
        "prev": null,
        "next": null
    },
    "meta": {
        "current_page": 1,
        "from": null,
        "last_page": 1,
        "per_page": 50,
        "to": null,
        "total": 0
    }
}
```

## Casos de Uso Práticos

### 1. Auditoria por Usuário Específico

Quando você precisa verificar todas as ações de um usuário específico, mas só lembra parte do username:

```http
GET /action-history?user_search=silva&per_page=100
```

### 2. Investigação de Problemas

Para investigar ações realizadas por usuários administrativos:

```http
GET /action-history?user_search=admin&action_type=DELETE&start_date=2025-01-20
```

### 3. Relatórios por Categoria de Usuário

Para gerar relatórios de ações realizadas por dentistas:

```http
GET /action-history?user_search=dr.&start_date=2025-01-01&end_date=2025-01-31
```

### 4. Monitoramento de Atividade

Para monitorar atividade de usuários específicos em tempo real:

```http
GET /action-history?user_search=operador&start_date=2025-01-27
```

## Performance e Considerações

### Otimização de Query

O filtro usa `whereHas` com `whereRaw`, que é eficiente para buscas em relacionamentos. Para melhor performance em grandes volumes de dados, considere:

1. **Índice no campo username**: 
   ```sql
   CREATE INDEX idx_users_username_lower ON users ((LOWER(username)));
   ```

2. **Limite de resultados**: Use sempre `per_page` para paginar resultados grandes.

### Limitações

- A busca é feita apenas no campo `username`, não em `name` ou outros campos do usuário
- Wildcards são aplicados automaticamente (não é possível busca exata sem wildcards)
- A busca é sempre parcial (contém o termo, não igualdade exata)

## Implementação Técnica

### Scope no Model

```php
public function scopeByUserSearch($query, string $userSearch)
{
    return $query->whereHas('user', function ($q) use ($userSearch) {
        $q->whereRaw('LOWER(username) LIKE ?', ['%' . strtolower($userSearch) . '%']);
    });
}
```

### Uso no Controller

```php
if ($request->has('user_search')) {
    $query->byUserSearch($request->user_search);
}
```

## Testes

Para testar o filtro, você pode usar os seguintes comandos:

```bash
# Teste básico
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost/action-history?user_search=daniel"

# Teste case-insensitive
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost/action-history?user_search=DANIEL"

# Teste com filtros combinados
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost/action-history?user_search=admin&action_type=CREATE"
```

O filtro `user_search` está agora totalmente implementado e pronto para uso em produção!
