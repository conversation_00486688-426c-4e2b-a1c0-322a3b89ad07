<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dentistas', function (Blueprint $table) {
            $table->string('profile_picture_url')->nullable()->default('profile_pic%252Fdefault.png')->after('observacoes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dentistas', function (Blueprint $table) {
            $table->dropColumn('profile_picture_url');
        });
    }
};
