<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('action_histories', function (Blueprint $table) {
            $table->id();

            // User who performed the action (always required)
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Action details
            $table->string('action_type'); // CREATE, UPDATE, DELETE
            $table->text('action_description'); // Human-readable description
            $table->string('http_method')->nullable(); // POST, PUT, PATCH, DELETE
            $table->string('endpoint')->nullable(); // API endpoint

            // Optional entity relationships
            $table->unsignedBigInteger('paciente_id')->nullable();
            $table->foreign('paciente_id')->references('id')->on('pacientes')->onDelete('set null');

            $table->unsignedBigInteger('dentista_id')->nullable();
            $table->foreign('dentista_id')->references('id')->on('dentistas')->onDelete('set null');

            $table->unsignedBigInteger('clinica_id')->nullable();
            $table->foreign('clinica_id')->references('id')->on('clinicas')->onDelete('set null');

            // Polymorphic relationship for future entity types
            $table->string('entity_type')->nullable(); // Model class name
            $table->unsignedBigInteger('entity_id')->nullable(); // Model ID
            $table->index(['entity_type', 'entity_id']);

            // Data tracking
            $table->json('old_data')->nullable(); // Before state
            $table->json('new_data')->nullable(); // After state

            // Additional metadata
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('action_type');
            $table->index('paciente_id');
            $table->index('dentista_id');
            $table->index('clinica_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('action_histories');
    }
};
