<template>
  <div class="custom-input-group">
    <!-- Addon à esquerda -->
    <div
      v-if="addonLeft"
      class="custom-input-group-addon custom-input-group-addon-left"
      :class="addonLeftClass"
    >
      <slot name="addon-left"></slot>
    </div>

    <!-- Input principal -->
    <div class="custom-input-group-input">
      <slot></slot>
    </div>

    <!-- Addon à direita -->
    <div
      v-if="addonRight && hasAddonRightContent"
      class="custom-input-group-addon custom-input-group-addon-right"
      :class="addonRightClass"
    >
      <slot name="addon-right"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomInputGroup',
  props: {
    addonLeft: {
      type: Boolean,
      default: false
    },
    addonRight: {
      type: Boolean,
      default: false
    },
    addonLeftClass: {
      type: String,
      default: ''
    },
    addonRightClass: {
      type: String,
      default: ''
    },
    hasAddonRightContent: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped>
.custom-input-group {
  display: flex;
  position: relative;
  width: 100%;
}

.custom-input-group-addon {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  min-width: 70px;
  justify-content: center;
}

.custom-input-group-addon-left {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
  border-right: 0;
}

.custom-input-group-addon-right {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-left: 0;
  min-width: 40px; /* Menor largura para o botão de limpar */
  padding: 0.25rem 0.5rem;
}

.custom-input-group-input {
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

/* Estilos para garantir que os inputs dentro do grupo tenham bordas corretas */
.custom-input-group-input :deep(input),
.custom-input-group-input :deep(select),
.custom-input-group-input :deep(textarea) {
  border-radius: 0;
}

.custom-input-group-input:first-child :deep(input),
.custom-input-group-input:first-child :deep(select),
.custom-input-group-input:first-child :deep(textarea) {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.custom-input-group-input:last-child :deep(input),
.custom-input-group-input:last-child :deep(select),
.custom-input-group-input:last-child :deep(textarea) {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

/* Ajustes para quando há addon à esquerda */
.custom-input-group-addon-left + .custom-input-group-input :deep(input),
.custom-input-group-addon-left + .custom-input-group-input :deep(select),
.custom-input-group-addon-left + .custom-input-group-input :deep(textarea) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Ajustes para quando há addon à direita */
/* O seletor :has não é suportado em todos os navegadores, então usamos uma abordagem alternativa */
.custom-input-group-addon-right ~ .custom-input-group-input :deep(input),
.custom-input-group-addon-right ~ .custom-input-group-input :deep(select),
.custom-input-group-addon-right ~ .custom-input-group-input :deep(textarea) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* Classe adicional para aplicar quando há addon à direita */
.has-addon-right :deep(input),
.has-addon-right :deep(select),
.has-addon-right :deep(textarea) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
</style>
