<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\Paciente;
use App\Models\Modelo3D;

class Modelo3DController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $modelos3d = Modelo3D::all();

            // Formatar as URLs dos modelos 3D
            $modelos3d = $modelos3d->map(function ($modelo) {
                $modelo->url = modelo3dUrl($modelo->url);
                return $modelo;
            });

            return response()->json($modelos3d);
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return $this->uploadModelo3D($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $modelo3d = Modelo3D::findOrFail($id);
            $modelo3d->url = modelo3dUrl($modelo3d->url);

            return response()->json($modelo3d);
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $modelo3d = Modelo3D::findOrFail($id);

            if ($request->has('descricao')) {
                $modelo3d->descricao = $request->descricao;
            }

            if ($request->has('data')) {
                $modelo3d->data = $request->data;
            }

            if ($request->has('is_diagnostico')) {
                $modelo3d->is_diagnostico = (bool)$request->is_diagnostico;
            }

            if ($request->has('tag_diagnostico')) {
                $modelo3d->tag_diagnostico = $request->tag_diagnostico;
            }

            $modelo3d->save();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $modelo3d = Modelo3D::findOrFail($id);

            // Remover o arquivo físico
            $path = 'modelos3d/' . $modelo3d->dir . '/' . $modelo3d->filename;
            if (Storage::disk('local')->exists($path)) {
                Storage::disk('local')->delete($path);
            }

            // Remover o registro do banco de dados
            $modelo3d->delete();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove models by patient ID and date.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function deleteByDateAndPatient(Request $request)
    {
        try {
            // Validar os parâmetros recebidos
            $request->validate([
                'paciente_id' => 'required|integer',
                'data' => 'required|date_format:Y-m-d',
            ]);

            $paciente_id = $request->input('paciente_id');
            $data = $request->input('data');

            // Buscar modelos 3D que correspondem ao paciente_id e data
            $modelos3d = Modelo3D::where('paciente_id', $paciente_id)
                            ->where('data', $data)
                            ->get();

            if ($modelos3d->isEmpty()) {
                return responseError([
                    'message' => 'Nenhum modelo 3D encontrado para este paciente nesta data.'
                ]);
            }

            // Excluir cada modelo 3D encontrado
            foreach ($modelos3d as $modelo3d) {
                // Remover o arquivo físico
                $path = 'modelos3d/' . $modelo3d->dir . '/' . $modelo3d->filename;
                if (Storage::disk('local')->exists($path)) {
                    Storage::disk('local')->delete($path);
                }

                // Remover o registro do banco de dados
                $modelo3d->delete();
            }

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }
    public function uploadModelo3D(Request $request)
    {
        try {
            if (!$request->hasFile('modelo3d'))
                throw new \Exception('Modelo 3D inválido');

            $modelo = $request->file('modelo3d');

            $dir = $request->input('dir') ? $request->input('dir') : 'misc';

            $uniqueFilename = $this->getUniqueFilename();
            $fileExtension = $modelo->getClientOriginalExtension();

            $filename = $uniqueFilename . '.' . $fileExtension;

            $modelo_url = $dir . '/' . $filename;

            Storage::disk('local')->put('modelos3d/' . $modelo_url, file_get_contents($modelo), 'public');

            $paciente_id = $request->input('paciente_id');
            $data = $request->input('data');
            $descricao = $request->input('descricao') ? $request->input('descricao') : '';
            $is_diagnostico = $request->input('is_diagnostico') ? (bool)$request->input('is_diagnostico') : false;
            $tag_diagnostico = $request->input('tag_diagnostico') ? $request->input('tag_diagnostico') : null;

            $modelo3d = new Modelo3D();
            $modelo3d->paciente_id = $paciente_id;
            $modelo3d->dir = $dir;
            $modelo3d->filename = $filename;

            // Double encoded because of %2F ('/' - slash) character bug
            $encoded_url = urlencode(urlencode($modelo_url));
            $modelo3d->url = $encoded_url;

            $modelo3d->data = $data;
            $modelo3d->descricao = $descricao;
            $modelo3d->is_diagnostico = $is_diagnostico;
            $modelo3d->tag_diagnostico = $tag_diagnostico;
            $modelo3d->save();

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage()
            ]);
        }

        return $response;
    }

    public function serveModelo3D(Request $request, $url)
    {
        // Decodifica a URL
        $decodedUrl = urldecode($url);
        $decodedUrl = urldecode($decodedUrl);

        // Extrai o diretório e o nome do arquivo
        $parts = explode('/', $decodedUrl);
        $filename = end($parts);
        $directory = implode('/', array_slice($parts, 0, -1));

        // Verifica se o diretório e o nome do arquivo foram fornecidos
        if (!$directory || !$filename) {
            return response()->json(['error' => 'Diretório e nome do arquivo são obrigatórios'], 400);
        }

        // Verifica se o arquivo existe no diretório
        $path = storage_path('app/modelos3d/' . $directory . '/' . $filename);
        if (!file_exists($path)) {
            return response()->json(['error' => 'Arquivo não encontrado'], 404);
        }

        // Serve o modelo 3D
        $mimeType = mime_content_type($path);
        $headers = [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
        ];

        return response()->file($path, $headers);
    }

    private function getUniqueFilename()
    {
        return uniqid(rand(), true) . uniqid(rand(), true);
    }

    public function getModelos3DByPaciente($paciente_id)
    {
        try {
            $modelos3d = Modelo3D::where('paciente_id', $paciente_id)->get();

            // Formatar as URLs dos modelos 3D
            $modelos3d = $modelos3d->map(function ($modelo) {
                $modelo->url = modelo3dUrl($modelo->url);
                return $modelo;
            });

            return response()->json($modelos3d);
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }
}
