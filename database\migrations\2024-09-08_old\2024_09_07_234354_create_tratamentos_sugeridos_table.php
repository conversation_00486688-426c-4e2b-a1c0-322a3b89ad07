<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTratamentosSugeridosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tratamentos_sugeridos', function (Blueprint $table) {
            $table->id();
            $table->string('tag');
            $table->string('imagem_url');
            $table->string('tratamento');
            $table->text('observacao');
            $table->text('observacao_secundaria');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tratamentos_sugeridos');
    }
}
