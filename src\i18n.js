import { createI18n } from 'vue-i18n'

import br from './locale/br.ts'
import en from './locale/en.ts'
import es from './locale/es.ts'

function loadLocaleMessages() {
    const locales = [{ en: en }, { br: br }, { es: es }, { pt: br }]
    const messages = {}
    locales.forEach(lang => {
        const key = Object.keys(lang)
        messages[key] = lang[key]
    })
    return messages
}

const i18n = createI18n({
    locale: localStorage.getItem('app-language') || 'br',
    fallbackLocale: 'en',
    messages: loadLocaleMessages()
})

export const setI18nLanguage = (lang) => {
    if (i18n.global.locale !== lang) {
        localStorage.setItem('app-language', lang)
        i18n.global.locale = lang
    }
    return lang
}

export default i18n