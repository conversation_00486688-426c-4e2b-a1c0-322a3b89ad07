<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('dentistas', function (Blueprint $table) {
            $table->string('id_matricula')->after('clinica_id');
        });
    }

    public function down()
    {
        Schema::table('dentistas', function (Blueprint $table) {
            $table->dropColumn('id_matricula');
        });
    }
};