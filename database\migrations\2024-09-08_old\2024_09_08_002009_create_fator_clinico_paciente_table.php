<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fator_clinico_paciente', function (Blueprint $table) {
            $table->id();
            $table->unsignedBiginteger('fator_clinico_id');
            $table->unsignedBiginteger('paciente_id');

            $table->foreign('fator_clinico_id')->references('id')
                ->on('fatores_clinicos')->onDelete('cascade');
            $table->foreign('paciente_id')->references('id')
                ->on('pacientes')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fator_clinico_paciente');
    }
};
