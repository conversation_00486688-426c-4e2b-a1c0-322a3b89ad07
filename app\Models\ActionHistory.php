<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ActionHistory extends Model
{
    use HasFactory;

    protected $table = 'action_histories';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'action_type',
        'action_description',
        'http_method',
        'endpoint',
        'paciente_id',
        'dentista_id',
        'clinica_id',
        'entity_type',
        'entity_id',
        'old_data',
        'new_data',
        'ip_address',
        'user_agent',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'old_data' => 'array',
            'new_data' => 'array',
        ];
    }

    /**
     * Action types constants
     */
    const ACTION_CREATE = 'CREATE';
    const ACTION_UPDATE = 'UPDATE';
    const ACTION_DELETE = 'DELETE';

    /**
     * Get the user who performed the action.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the patient related to this action (if applicable).
     */
    public function paciente(): BelongsTo
    {
        return $this->belongsTo(Paciente::class, 'paciente_id');
    }

    /**
     * Get the dentist related to this action (if applicable).
     */
    public function dentista(): BelongsTo
    {
        return $this->belongsTo(Dentista::class, 'dentista_id');
    }

    /**
     * Get the clinica related to this action (if applicable).
     */
    public function clinica(): BelongsTo
    {
        return $this->belongsTo(Clinica::class, 'clinica_id');
    }

    /**
     * Get the polymorphic entity related to this action.
     */
    public function entity(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to filter by action type.
     */
    public function scopeByActionType($query, string $actionType)
    {
        return $query->where('action_type', $actionType);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by user search (username).
     */
    public function scopeByUserSearch($query, string $userSearch)
    {
        return $query->whereHas('user', function ($q) use ($userSearch) {
            $q->whereRaw('LOWER(username) LIKE ?', ['%' . strtolower($userSearch) . '%']);
        });
    }

    /**
     * Scope to filter by patient.
     */
    public function scopeByPaciente($query, int $pacienteId)
    {
        return $query->where('paciente_id', $pacienteId);
    }

    /**
     * Scope to filter by dentist.
     */
    public function scopeByDentista($query, int $dentistaId)
    {
        return $query->where('dentista_id', $dentistaId);
    }

    /**
     * Scope to filter by clinica.
     */
    public function scopeByClinica($query, int $clinicaId)
    {
        return $query->where('clinica_id', $clinicaId);
    }

    /**
     * Scope to filter by entity type.
     */
    public function scopeByEntityType($query, string $entityType)
    {
        return $query->where('entity_type', $entityType);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get a human-readable summary of the changes.
     */
    public function getChangesSummary(): array
    {
        if (!$this->old_data || !$this->new_data) {
            return [];
        }

        $changes = [];
        $oldData = $this->old_data;
        $newData = $this->new_data;

        foreach ($newData as $key => $newValue) {
            $oldValue = $oldData[$key] ?? null;

            if ($oldValue !== $newValue) {
                $changes[$key] = [
                    'old' => $oldValue,
                    'new' => $newValue,
                ];
            }
        }

        return $changes;
    }

    /**
     * Check if this action has data changes.
     */
    public function hasDataChanges(): bool
    {
        return !empty($this->getChangesSummary());
    }
}
