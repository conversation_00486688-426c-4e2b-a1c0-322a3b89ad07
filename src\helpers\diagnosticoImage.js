const imgFatoresClinicos = [
  'apinhamentos_severos_arcada_inferior',
  'apinhamentos_severos',
  'biotipo_facial_mesocefalico',
  'biprotrusao',
  'classe_i',
  'classe_ii_grande',
  'classe_ii_media',
  'classe_ii_pequena',
  'classe_iii_grande',
  'classe_iii_media',
  'classe_iii_pequena',
  'curva_de_spee_muito_acentuada',
  'pequenos_apinhamentos',
  'pequenos_apinhamentos_arcada_inferior',
  'protrusao_maxilar',
  'retrusao_mandibular',
];

const imgTratamentosSugeridos = [
  'cirurgia_ortognatica-classe_ii',
  'cirurgia_ortognatica-classe_iii',
  'desenvolvimento_transversal',
  'disjuncao_mascara_facial',
  'distalizacao_com_cursor',
  'distalizacao_e_intrusao',
  'distalizacao_unilateral',
  'elastico_classe_ii',
  'elastico_classe_iii',
  'elastico_classe_iii_desgastes_inferior',
  'protrator_mandibular',
  'retracao_inferior_buccal_shelf',
  'tratamento_simples',
];

import imgDefault from '@/assets/img/protocolos/default.png';

// Import all fatorClinico images statically
import apinhamentos_severos_arcada_inferior from '@/assets/img/fatores-clinicos/apinhamentos_severos_arcada_inferior.jpg';
import apinhamentos_severos from '@/assets/img/fatores-clinicos/apinhamentos_severos.jpg';
import biotipo_facial_mesocefalico from '@/assets/img/fatores-clinicos/biotipo_facial_mesocefalico.jpg';
import biprotrusao from '@/assets/img/fatores-clinicos/biprotrusao.jpg';
import classe_i from '@/assets/img/fatores-clinicos/classe_i.jpg';
import classe_ii_grande from '@/assets/img/fatores-clinicos/classe_ii_grande.jpg';
import classe_ii_media from '@/assets/img/fatores-clinicos/classe_ii_media.jpg';
import classe_ii_pequena from '@/assets/img/fatores-clinicos/classe_ii_pequena.jpg';
import classe_iii_grande from '@/assets/img/fatores-clinicos/classe_iii_grande.jpg';
import classe_iii_media from '@/assets/img/fatores-clinicos/classe_iii_media.jpg';
import classe_iii_pequena from '@/assets/img/fatores-clinicos/classe_iii_pequena.jpg';
import curva_de_spee_muito_acentuada from '@/assets/img/fatores-clinicos/curva_de_spee_muito_acentuada.jpg';
import pequenos_apinhamentos from '@/assets/img/fatores-clinicos/pequenos_apinhamentos.jpg';
import pequenos_apinhamentos_arcada_inferior from '@/assets/img/fatores-clinicos/pequenos_apinhamentos_arcada_inferior.jpg';
import protrusao_maxilar from '@/assets/img/fatores-clinicos/protrusao_maxilar.jpg';
import retrusao_mandibular from '@/assets/img/fatores-clinicos/retrusao_mandibular.jpg';

// Import all tratamentoSugerido images statically
import cirurgia_ortognatica_classe_ii from '@/assets/img/tratamentos-sugeridos/cirurgia_ortognatica-classe_ii.jpg';
import cirurgia_ortognatica_classe_iii from '@/assets/img/tratamentos-sugeridos/cirurgia_ortognatica-classe_iii.jpg';
import desenvolvimento_transversal from '@/assets/img/tratamentos-sugeridos/desenvolvimento_transversal.jpg';
import disjuncao_mascara_facial from '@/assets/img/tratamentos-sugeridos/disjuncao_mascara_facial.jpg';
import distalizacao_com_cursor from '@/assets/img/tratamentos-sugeridos/distalizacao_com_cursor.jpg';
import distalizacao_e_intrusao from '@/assets/img/tratamentos-sugeridos/distalizacao_e_intrusao.jpg';
import distalizacao_unilateral from '@/assets/img/tratamentos-sugeridos/distalizacao_unilateral.jpg';
import elastico_classe_ii from '@/assets/img/tratamentos-sugeridos/elastico_classe_ii.jpg';
import elastico_classe_iii from '@/assets/img/tratamentos-sugeridos/elastico_classe_iii.jpg';
import elastico_classe_iii_desgastes_inferior from '@/assets/img/tratamentos-sugeridos/elastico_classe_iii_desgastes_inferior.jpg';
import protrator_mandibular from '@/assets/img/tratamentos-sugeridos/protrator_mandibular.jpg';
import retracao_inferior_buccal_shelf from '@/assets/img/tratamentos-sugeridos/retracao_inferior_buccal_shelf.jpg';
import tratamento_simples from '@/assets/img/tratamentos-sugeridos/tratamento_simples.jpg';

const fatorClinicoImages = {
  apinhamentos_severos_arcada_inferior,
  apinhamentos_severos,
  biotipo_facial_mesocefalico,
  biprotrusao,
  classe_i,
  classe_ii_grande,
  classe_ii_media,
  classe_ii_pequena,
  classe_iii_grande,
  classe_iii_media,
  classe_iii_pequena,
  curva_de_spee_muito_acentuada,
  pequenos_apinhamentos,
  pequenos_apinhamentos_arcada_inferior,
  protrusao_maxilar,
  retrusao_mandibular,
};

const tratamentoSugeridoImages = {
  cirurgia_ortognatica_classe_ii,
  cirurgia_ortognatica_classe_iii,
  desenvolvimento_transversal,
  disjuncao_mascara_facial,
  distalizacao_com_cursor,
  distalizacao_e_intrusao,
  distalizacao_unilateral,
  elastico_classe_ii,
  elastico_classe_iii,
  elastico_classe_iii_desgastes_inferior,
  protrator_mandibular,
  retracao_inferior_buccal_shelf,
  tratamento_simples,
};

/**
 * Returns the image path for the given fator clinico tag.
 * If the image does not exist, returns the default image.
 * @param {string} type
 * @param {string} tag
 * @returns {string} image path
 */
export async function diagnosticoImage(type, tag) {
  try {
    // Usa template literals para construir o caminho dinamicamente
    const image = await import(
      /* webpackMode: "lazy-once" */
      `@/assets/img/${type === 'fatorClinico' ? 'fatores-clinicos' : 'tratamentos-sugeridos'}/${tag}.jpg`
    );
    return image.default;
  } catch (e) {
    return imgDefault;
  }
}

