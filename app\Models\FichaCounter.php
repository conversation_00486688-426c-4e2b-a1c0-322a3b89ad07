<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class FichaCounter extends Model
{
    protected $primaryKey = 'clinica_id'; // PK customizada
    public $incrementing = false; // Desativa auto-increment
    protected $guarded = [];

    /**
     * Gera o próximo id_ficha para uma clínica (com lock para evitar concorrência)
     */
    public static function getNextIdFicha($clinica_id)
    {
        return DB::transaction(function () use ($clinica_id) {
            $counter = self::lockForUpdate()->firstOrCreate(
                ['clinica_id' => $clinica_id],
                ['last_id_ficha' => 0]
            );

            $counter->increment('last_id_ficha');
            return $counter->last_id_ficha;
        });
    }
}