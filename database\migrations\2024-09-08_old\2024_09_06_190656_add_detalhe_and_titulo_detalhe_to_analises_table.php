<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDetalheAndTituloDetalheToAnalisesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('analises', function (Blueprint $table) {
            $table->string('detalhe')->nullable();
            $table->string('titulo_detalhe')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('analises', function (Blueprint $table) {
            $table->dropColumn('detalhe');
            $table->dropColumn('titulo_detalhe');
        });
    }
}