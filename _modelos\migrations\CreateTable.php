<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $table = 'table_name';

    public function up(): void
    {
        Schema::create($this->table, function (Blueprint $table) {
            $table->id();

            // FK - using conventional names:
            $table->foreignId('other_table_id')->constrained();

            // FK - using custom names:
            $table->unsignedBigInteger('id_other_table');
            $table->foreign('id_other_table')->references('id')->on('other_table');

            $table->string('string_name');
            $table->text('text_name')->nullable();
            $table->integer('integer_name');
            $table->timestamp('timestamp_name')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists($this->table);
    }
};
