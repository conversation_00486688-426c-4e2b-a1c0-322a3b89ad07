<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class MatriculaCounter extends Model
{
    protected $primaryKey = 'clinica_id'; // PK customizada
    public $incrementing = false; // Desativa auto-increment
    protected $guarded = [];

    /**
     * Gera o próximo id_matricula para uma clínica (com lock para evitar concorrência)
     */
    public static function getNextIdMatricula($clinica_id)
    {
        return DB::transaction(function () use ($clinica_id) {
            $counter = self::lockForUpdate()->firstOrCreate(
                ['clinica_id' => $clinica_id],
                ['last_id_matricula' => 0]
            );

            $counter->increment('last_id_matricula');
            return $counter->last_id_matricula;
        });
    }
}
